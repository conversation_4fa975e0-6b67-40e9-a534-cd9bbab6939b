package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram"
	"github.com/jony4/52kanduanju.mp/server/config"
	"github.com/jony4/52kanduanju.mp/server/dao"
	"github.com/jony4/52kanduanju.mp/server/middleware"
	"github.com/jony4/52kanduanju.mp/server/models"
	"gorm.io/gorm"
)

// WechatService 微信服务 (使用PowerWeChat)
type WechatService struct {
	db          *gorm.DB
	config      *config.Config
	userDAO     *dao.UserDAO
	miniProgram *miniProgram.MiniProgram
}

// NewWechatService 创建微信服务
func NewWechatService(db *gorm.DB, cfg *config.Config) *WechatService {
	// 初始化PowerWeChat小程序配置
	miniProgramConfig := &miniProgram.UserConfig{
		AppID:     cfg.Wechat.AppID,
		Secret:    cfg.Wechat.AppSecret,
		HttpDebug: cfg.Server.Mode == "debug",
		Debug:     cfg.Server.Mode == "debug",
	}

	// 创建小程序实例
	miniProgramApp, err := miniProgram.NewMiniProgram(miniProgramConfig)
	if err != nil {
		// 如果初始化失败，记录错误但不阻止服务启动
		fmt.Printf("Warning: Failed to initialize WeChat MiniProgram: %v\n", err)
	}

	return &WechatService{
		db:          db,
		config:      cfg,
		userDAO:     dao.NewUserDAO(db),
		miniProgram: miniProgramApp,
	}
}

// WechatLogin 微信登录
func (s *WechatService) WechatLogin(req *models.WechatLoginRequest) (*models.WechatLoginResponse, error) {
	if s.miniProgram == nil {
		return nil, errors.New("微信小程序未正确初始化")
	}

	ctx := context.Background()

	// 1. 调用PowerWeChat获取session信息
	sessionResult, err := s.miniProgram.Auth.Session(ctx, req.Code)
	if err != nil {
		return nil, fmt.Errorf("微信登录失败: %v", err)
	}

	// 检查是否有错误
	if sessionResult.ErrCode != 0 {
		return nil, fmt.Errorf("微信登录失败: %s", sessionResult.ErrMsg)
	}

	// 2. 根据openid查找或创建用户
	user, err := s.findOrCreateUser(sessionResult.OpenID, sessionResult.UnionID, sessionResult.SessionKey)
	if err != nil {
		return nil, err
	}

	// 3. 生成JWT token
	token, err := middleware.GenerateToken(user.ID)
	if err != nil {
		return nil, err
	}

	// 4. 返回登录响应
	return &models.WechatLoginResponse{
		Token: token,
		User:  *user,
	}, nil
}

// DecryptPhoneNumber 解密微信手机号
func (s *WechatService) DecryptPhoneNumber(userID uint64, req *models.WechatPhoneRequest) (*models.WechatPhoneResponse, error) {
	if s.miniProgram == nil {
		return nil, errors.New("微信小程序未正确初始化")
	}

	// 1. 获取用户信息
	user, err := s.userDAO.GetByID(userID)
	if err != nil {
		return nil, err
	}

	// 2. 获取session_key
	sessionKey := req.SessionKey
	if sessionKey == "" && user.SessionKey != nil {
		sessionKey = *user.SessionKey
	}

	if sessionKey == "" {
		return nil, errors.New("缺少session_key，请重新登录")
	}

	ctx := context.Background()

	// 3. 使用PowerWeChat解密手机号
	phoneData, err := s.miniProgram.Encryptor.DecryptData(ctx, sessionKey, req.EncryptedData, req.IV)
	if err != nil {
		return nil, fmt.Errorf("解密手机号失败: %v", err)
	}

	// 4. 解析手机号信息
	var phoneInfo models.WechatPhoneInfo
	if err := phoneData.To(&phoneInfo); err != nil {
		return nil, fmt.Errorf("解析手机号信息失败: %v", err)
	}

	// 5. 更新用户手机号
	err = s.userDAO.UpdatePhone(userID, phoneInfo.PhoneNumber)
	if err != nil {
		return nil, err
	}

	// 6. 返回手机号信息
	return &models.WechatPhoneResponse{
		PhoneNumber:     phoneInfo.PhoneNumber,
		PurePhoneNumber: phoneInfo.PurePhoneNumber,
		CountryCode:     phoneInfo.CountryCode,
	}, nil
}

// UpdateUserProfile 更新用户信息
func (s *WechatService) UpdateUserProfile(userID uint64, req *models.UserProfileUpdateRequest) (*models.User, error) {
	// 1. 获取用户信息
	user, err := s.userDAO.GetByID(userID)
	if err != nil {
		return nil, err
	}

	// 2. 更新用户信息
	if req.Nickname != nil {
		user.Nickname = req.Nickname
	}
	if req.Avatar != nil {
		user.Avatar = req.Avatar
	}

	// 3. 保存用户信息
	err = s.userDAO.Update(user)
	if err != nil {
		return nil, err
	}

	return user, nil
}

// findOrCreateUser 根据openid查找或创建用户
func (s *WechatService) findOrCreateUser(openid, unionid, sessionKey string) (*models.User, error) {
	// 1. 根据openid查找用户
	user, err := s.userDAO.GetByWechatOpenID(openid)
	if err == nil {
		// 用户存在，更新session_key
		user.SessionKey = &sessionKey
		err = s.userDAO.Update(user)
		if err != nil {
			return nil, err
		}
		return user, nil
	}

	// 2. 用户不存在，创建新用户
	now := time.Now()
	username := fmt.Sprintf("wx_%s", openid[len(openid)-8:])
	nickname := fmt.Sprintf("用户%s", openid[len(openid)-6:])

	newUser := &models.User{
		CreatedAt:    &now,
		UpdatedAt:    &now,
		Username:     &username,
		Nickname:     &nickname,
		WechatOpenID: &openid,
		SessionKey:   &sessionKey,
	}

	// 如果有unionid，也保存
	if unionid != "" {
		newUser.WechatUnionID = &unionid
	}

	// 3. 保存新用户
	err = s.userDAO.Create(newUser)
	if err != nil {
		return nil, err
	}

	return newUser, nil
}

// GetMiniProgram 获取小程序实例（用于其他高级功能）
func (s *WechatService) GetMiniProgram() *miniProgram.MiniProgram {
	return s.miniProgram
}

// IsConfigured 检查微信配置是否正确
func (s *WechatService) IsConfigured() bool {
	return s.miniProgram != nil && s.config.Wechat.AppID != "" && s.config.Wechat.AppSecret != ""
}
