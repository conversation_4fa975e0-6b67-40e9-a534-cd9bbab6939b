package services

import (
	"github.com/jony4/52kanduanju.mp/server/config"
	"github.com/jony4/52kanduanju.mp/server/dao"
	"github.com/jony4/52kanduanju.mp/server/models"
	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct {
	db      *gorm.DB
	config  *config.Config
	userDAO *dao.UserDAO
}

// NewUserService 创建用户服务
func NewUserService(db *gorm.DB, cfg *config.Config) *UserService {
	return &UserService{
		db:      db,
		config:  cfg,
		userDAO: dao.NewUserDAO(db),
	}
}

// GetUserByID 根据ID获取用户信息
func (s *UserService) GetUserByID(userID uint64) (*models.User, error) {
	return s.userDAO.GetByID(userID)
}

// UpdateUserProfile 更新用户信息
func (s *UserService) UpdateUserProfile(userID uint64, req *models.UserProfileUpdateRequest) (*models.User, error) {
	// 1. 获取用户信息
	user, err := s.userDAO.GetByID(userID)
	if err != nil {
		return nil, err
	}

	// 2. 更新用户信息
	if req.Nickname != nil {
		user.Nickname = req.Nickname
	}
	if req.Avatar != nil {
		user.Avatar = req.Avatar
	}

	// 3. 保存用户信息
	err = s.userDAO.Update(user)
	if err != nil {
		return nil, err
	}

	return user, nil
}

// UpdateUserPhone 更新用户手机号
func (s *UserService) UpdateUserPhone(userID uint64, phone string) error {
	return s.userDAO.UpdatePhone(userID, phone)
}

// CreateUser 创建用户
func (s *UserService) CreateUser(user *models.User) error {
	return s.userDAO.Create(user)
}

// GetUserByWechatOpenID 根据微信OpenID获取用户
func (s *UserService) GetUserByWechatOpenID(openid string) (*models.User, error) {
	return s.userDAO.GetByWechatOpenID(openid)
}
