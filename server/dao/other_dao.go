package dao

import (
	"time"

	"github.com/jony4/52kanduanju.mp/server/models"
	"gorm.io/gorm"
)

// DuanjuActorCutDAO 演员精彩片段数据访问层 (新结构)
type DuanjuActorCutDAO struct {
	*BaseDAO
}

func NewDuanjuActorCutDAO(db *gorm.DB) *DuanjuActorCutDAO {
	return &DuanjuActorCutDAO{
		BaseDAO: NewBaseDAO(db),
	}
}

// GetByID 根据ID获取演员精彩片段
func (c *DuanjuActorCutDAO) GetByID(id uint64) (*models.DuanjuActorCut, error) {
	var cut models.DuanjuActorCut
	err := c.DB.Where("id = ?", id).First(&cut).Error
	if err != nil {
		return nil, err
	}
	return &cut, nil
}

// Search 搜索演员精彩片段
func (c *DuanjuActorCutDAO) Search(keyword string, pagination *PaginationParams) ([]models.DuanjuActorCut, *PaginationResult, error) {
	var cuts []models.DuanjuActorCut
	var total int64

	query := c.DB.Model(&models.DuanjuActorCut{}).Where("title LIKE ?", "%"+keyword+"%")

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// 分页查询
	offset := pagination.GetOffset()
	err := query.Order("play_count DESC").
		Offset(offset).Limit(pagination.Limit).Find(&cuts).Error
	if err != nil {
		return nil, nil, err
	}

	paginationResult := NewPaginationResult(pagination.Page, pagination.Limit, total)
	return cuts, paginationResult, nil
}

// DuanjuSeriesCutDAO 短剧剧集精彩片段数据访问层 (新结构)
type DuanjuSeriesCutDAO struct {
	*BaseDAO
}

func NewDuanjuSeriesCutDAO(db *gorm.DB) *DuanjuSeriesCutDAO {
	return &DuanjuSeriesCutDAO{
		BaseDAO: NewBaseDAO(db),
	}
}

// GetByID 根据ID获取短剧剧集精彩片段
func (c *DuanjuSeriesCutDAO) GetByID(id uint64) (*models.DuanjuSeriesCut, error) {
	var cut models.DuanjuSeriesCut
	err := c.DB.Where("id = ?", id).First(&cut).Error
	if err != nil {
		return nil, err
	}
	return &cut, nil
}

// Search 搜索短剧剧集精彩片段
func (c *DuanjuSeriesCutDAO) Search(keyword string, pagination *PaginationParams) ([]models.DuanjuSeriesCut, *PaginationResult, error) {
	var cuts []models.DuanjuSeriesCut
	var total int64

	query := c.DB.Model(&models.DuanjuSeriesCut{}).Where("title LIKE ?", "%"+keyword+"%")

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// 分页查询
	offset := pagination.GetOffset()
	err := query.Order("play_count DESC").
		Offset(offset).Limit(pagination.Limit).Find(&cuts).Error
	if err != nil {
		return nil, nil, err
	}

	paginationResult := NewPaginationResult(pagination.Page, pagination.Limit, total)
	return cuts, paginationResult, nil
}

// DuanjuNewsDAO 新闻数据访问层
type DuanjuNewsDAO struct {
	*BaseDAO
}

func NewDuanjuNewsDAO(db *gorm.DB) *DuanjuNewsDAO {
	return &DuanjuNewsDAO{
		BaseDAO: NewBaseDAO(db),
	}
}

// Search 搜索新闻
func (n *DuanjuNewsDAO) Search(keyword string, pagination *PaginationParams) ([]models.DuanjuNews, *PaginationResult, error) {
	var news []models.DuanjuNews
	var total int64

	query := n.DB.Model(&models.DuanjuNews{}).Where("status = 1 AND title LIKE ?", "%"+keyword+"%")

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// 分页查询
	offset := pagination.GetOffset()
	err := query.Order("created_at DESC").
		Offset(offset).Limit(pagination.Limit).Find(&news).Error
	if err != nil {
		return nil, nil, err
	}

	paginationResult := NewPaginationResult(pagination.Page, pagination.Limit, total)
	return news, paginationResult, nil
}

// GetByDuanjuID 根据短剧ID获取相关新闻
func (n *DuanjuNewsDAO) GetByDuanjuID(duanjuID uint64, pagination *PaginationParams) ([]models.DuanjuNews, *PaginationResult, error) {
	var news []models.DuanjuNews
	var total int64

	query := n.DB.Model(&models.DuanjuNews{}).Where("duanju_id = ? AND status = 1", duanjuID)

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// 分页查询
	offset := pagination.GetOffset()
	err := query.Order("created_at DESC").
		Offset(offset).Limit(pagination.Limit).Find(&news).Error
	if err != nil {
		return nil, nil, err
	}

	paginationResult := NewPaginationResult(pagination.Page, pagination.Limit, total)
	return news, paginationResult, nil
}

// GetByActorID 根据演员ID获取相关新闻
func (n *DuanjuNewsDAO) GetByActorID(actorID uint64, pagination *PaginationParams) ([]models.DuanjuNews, *PaginationResult, error) {
	var news []models.DuanjuNews
	var total int64

	query := n.DB.Model(&models.DuanjuNews{}).Where("actor_id = ? AND status = 1", actorID)

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, err
	}

	// 分页查询
	offset := pagination.GetOffset()
	err := query.Order("created_at DESC").
		Offset(offset).Limit(pagination.Limit).Find(&news).Error
	if err != nil {
		return nil, nil, err
	}

	paginationResult := NewPaginationResult(pagination.Page, pagination.Limit, total)
	return news, paginationResult, nil
}

// Create 创建新闻
func (n *DuanjuNewsDAO) Create(news *models.DuanjuNews) error {
	return n.DB.Create(news).Error
}

// GetByID 根据ID获取新闻
func (n *DuanjuNewsDAO) GetByID(id uint64) (*models.DuanjuNews, error) {
	var news models.DuanjuNews
	err := n.DB.Preload("Duanju").Preload("Actor").Where("id = ? AND status = 1", id).First(&news).Error
	if err != nil {
		return nil, err
	}
	return &news, nil
}

// PaymentDAO 付费订单数据访问层
type PaymentDAO struct {
	*BaseDAO
}

func NewPaymentDAO(db *gorm.DB) *PaymentDAO {
	return &PaymentDAO{
		BaseDAO: NewBaseDAO(db),
	}
}

// CreateOrder 创建付费订单
func (p *PaymentDAO) CreateOrder(order *models.PaymentOrder) error {
	return p.DB.Create(order).Error
}

// GetPaymentStatus 获取用户对内容的付费状态
func (p *PaymentDAO) GetPaymentStatus(userID uint64, contentType, contentID string) (*models.PaymentOrder, error) {
	var order models.PaymentOrder
	err := p.DB.Where("user_id = ? AND content_type = ? AND content_id = ? AND status = 'paid'",
		userID, contentType, contentID).First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// UpdateOrderStatus 更新订单状态
func (p *PaymentDAO) UpdateOrderStatus(orderID, status string, unlockedAt *time.Time) error {
	updates := map[string]interface{}{
		"status": status,
	}
	if unlockedAt != nil {
		updates["unlocked_at"] = unlockedAt
	}
	return p.DB.Model(&models.PaymentOrder{}).Where("id = ?", orderID).Updates(updates).Error
}

// DailyFortuneDAO 今日运势数据访问层
type DailyFortuneDAO struct {
	*BaseDAO
}

func NewDailyFortuneDAO(db *gorm.DB) *DailyFortuneDAO {
	return &DailyFortuneDAO{
		BaseDAO: NewBaseDAO(db),
	}
}

// GetTodayFortune 获取今日运势 (使用新的字段结构)
func (d *DailyFortuneDAO) GetTodayFortune() (*models.DailyFortune, error) {
	var fortune models.DailyFortune
	today := time.Now().Format("2006-01-02")
	err := d.DB.Where("today = ?", today).First(&fortune).Error
	if err != nil {
		return nil, err
	}
	return &fortune, nil
}

// UserDAO 用户数据访问层
type UserDAO struct {
	*BaseDAO
}

func NewUserDAO(db *gorm.DB) *UserDAO {
	return &UserDAO{
		BaseDAO: NewBaseDAO(db),
	}
}

// GetByID 根据ID获取用户
func (u *UserDAO) GetByID(id uint64) (*models.User, error) {
	var user models.User
	err := u.DB.Where("id = ?", id).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByWechatOpenID 根据微信OpenID获取用户
func (u *UserDAO) GetByWechatOpenID(openid string) (*models.User, error) {
	var user models.User
	err := u.DB.Where("wechat_openid = ?", openid).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// Create 创建用户
func (u *UserDAO) Create(user *models.User) error {
	return u.DB.Create(user).Error
}

// Update 更新用户
func (u *UserDAO) Update(user *models.User) error {
	return u.DB.Save(user).Error
}

// UpdatePhone 更新用户手机号
func (u *UserDAO) UpdatePhone(userID uint64, phone string) error {
	return u.DB.Model(&models.User{}).Where("id = ?", userID).Update("phone", phone).Error
}

// AppConfigDAO 应用配置数据访问层
type AppConfigDAO struct {
	*BaseDAO
}

func NewAppConfigDAO(db *gorm.DB) *AppConfigDAO {
	return &AppConfigDAO{
		BaseDAO: NewBaseDAO(db),
	}
}

// GetByKey 根据配置键获取配置值
func (a *AppConfigDAO) GetByKey(key string) (*models.AppConfig, error) {
	var config models.AppConfig
	err := a.DB.Where("config_key = ?", key).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// GetAll 获取所有配置
func (a *AppConfigDAO) GetAll() ([]models.AppConfig, error) {
	var configs []models.AppConfig
	err := a.DB.Find(&configs).Error
	return configs, err
}
