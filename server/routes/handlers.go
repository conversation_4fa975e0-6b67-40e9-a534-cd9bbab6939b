package routes

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/jony4/52kanduanju.mp/server/config"
	"github.com/jony4/52kanduanju.mp/server/middleware"
	"github.com/jony4/52kanduanju.mp/server/models"
	"github.com/jony4/52kanduanju.mp/server/services"
	"gorm.io/gorm"
)

type Controllers struct {
	recommendationService *services.RecommendationService
	searchService         *services.SearchService
	dramaService          *services.DuanjuService
	actorService          *services.DuanjuActorService
	commentService        *services.CommentService
	tagService            *services.TagService
	paymentService        *services.PaymentService
	wechatService         *services.WechatService
	userService           *services.UserService
}

func NewControllers(db *gorm.DB, cfg *config.Config) *Controllers {
	return &Controllers{
		recommendationService: services.NewRecommendationService(db, cfg),
		searchService:         services.NewSearchService(db, cfg),
		dramaService:          services.NewDuanjuService(db, cfg),
		actorService:          services.NewDuanjuActorService(db, cfg),
		commentService:        services.NewCommentService(db, cfg),
		tagService:            services.NewTagService(db, cfg),
		paymentService:        services.NewPaymentService(db, cfg),
		wechatService:         services.NewWechatService(db, cfg),
		userService:           services.NewUserService(db, cfg),
	}
}

// --- Helper Functions ---

func (ctrl *Controllers) sendSuccess(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, models.APIResponse{
		Code:    200,
		Message: "success",
		Data:    data,
	})
}

func (ctrl *Controllers) sendError(c *gin.Context, code int, message string, err error) {
	response := models.APIResponse{
		Code:    code,
		Message: message,
	}
	if err != nil {
		// Don't expose internal errors to clients in production
		if config.GlobalConfig.Server.Mode == "debug" {
			response.Error = err.Error()
		} else {
			// Log the actual error for debugging
			fmt.Printf("Error: %v\n", err)
		}
	}
	c.JSON(code, response)
}

func (ctrl *Controllers) getUserID(c *gin.Context) uint64 {
	return middleware.GetUserID(c)
}

// --- Recommendation Handlers ---

func (ctrl *Controllers) GetHomeRecommendation(c *gin.Context) {
	data, err := ctrl.recommendationService.GetHomeRecommendation()
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "获取推荐内容失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

// --- Search Handlers ---

func (ctrl *Controllers) Search(c *gin.Context) {
	var req models.SearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	data, err := ctrl.searchService.Search(&req)
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "搜索失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

// --- Drama Handlers ---

func (ctrl *Controllers) GetDramas(c *gin.Context) {
	category := c.Query("category")
	platform := c.Query("tag") // 使用platform参数替代tag
	sort := c.DefaultQuery("sort", "hot")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	data, err := ctrl.dramaService.GetDuanjuList(category, platform, sort, page, limit)
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "获取短剧列表失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

func (ctrl *Controllers) GetDramaByID(c *gin.Context) {
	dramaID := c.Param("dramaId")
	userID := ctrl.getUserID(c)

	data, err := ctrl.dramaService.GetDuanjuDetail(dramaID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			ctrl.sendError(c, http.StatusNotFound, "短剧不存在", err)
		} else {
			ctrl.sendError(c, http.StatusInternalServerError, "获取短剧详情失败", err)
		}
		return
	}

	ctrl.sendSuccess(c, data)
}

func (ctrl *Controllers) GetDramaCuts(c *gin.Context) {
	dramaID := c.Param("dramaId")
	userID := ctrl.getUserID(c)
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	data, err := ctrl.dramaService.GetDuanjuSeriesCuts(dramaID, userID, page, limit)
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "获取精彩片段失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

func (ctrl *Controllers) GetDramaNews(c *gin.Context) {
	dramaID := c.Param("dramaId")

	data, err := ctrl.dramaService.GetDuanjuNews(dramaID)
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "获取新闻失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

// --- Actor Handlers ---

func (ctrl *Controllers) GetActorByID(c *gin.Context) {
	actorID := c.Param("actorId")
	userID := ctrl.getUserID(c)

	data, err := ctrl.actorService.GetActorDetail(actorID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			ctrl.sendError(c, http.StatusNotFound, "演员不存在", err)
		} else {
			ctrl.sendError(c, http.StatusInternalServerError, "获取演员详情失败", err)
		}
		return
	}

	ctrl.sendSuccess(c, data)
}

func (ctrl *Controllers) GetActorDramas(c *gin.Context) {
	actorID := c.Param("actorId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	data, err := ctrl.actorService.GetActorDuanjus(actorID, page, limit)
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "获取演员作品失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

func (ctrl *Controllers) GetActorCuts(c *gin.Context) {
	actorID := c.Param("actorId")
	userID := ctrl.getUserID(c)

	data, err := ctrl.actorService.GetActorCuts(actorID, userID)
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "获取演员精彩片段失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

func (ctrl *Controllers) FollowActor(c *gin.Context) {
	actorID := c.Param("actorId")
	// 从认证中间件获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		ctrl.sendError(c, http.StatusUnauthorized, "请先登录", nil)
		return
	}

	err := ctrl.actorService.FollowActor(userID.(uint64), actorID)
	if err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "关注失败", err)
		return
	}

	ctrl.sendSuccess(c, nil)
}

func (ctrl *Controllers) UnfollowActor(c *gin.Context) {
	actorID := c.Param("actorId")
	// 从认证中间件获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		ctrl.sendError(c, http.StatusUnauthorized, "请先登录", nil)
		return
	}

	err := ctrl.actorService.UnfollowActor(userID.(uint64), actorID)
	if err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "取消关注失败", err)
		return
	}

	ctrl.sendSuccess(c, nil)
}

// ToggleFollowActor 切换关注状态（关注/取消关注）
func (ctrl *Controllers) ToggleFollowActor(c *gin.Context) {
	actorID := c.Param("actorId")
	// 从认证中间件获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		ctrl.sendError(c, http.StatusUnauthorized, "请先登录", nil)
		return
	}

	isFollowed, err := ctrl.actorService.ToggleFollowActor(userID.(uint64), actorID)
	if err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "操作失败", err)
		return
	}

	ctrl.sendSuccess(c, gin.H{
		"isFollowed": isFollowed,
		"message":    map[bool]string{true: "关注成功", false: "取消关注"}[isFollowed],
	})
}

// --- Comment Handlers ---

func (ctrl *Controllers) GetComments(c *gin.Context) {
	// 支持两种参数名格式
	targetType := c.Query("targetType")
	if targetType == "" {
		targetType = c.Query("contentType")
	}

	targetID := c.Query("targetId")
	if targetID == "" {
		targetID = c.Query("contentId")
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if targetType == "" || targetID == "" {
		ctrl.sendError(c, http.StatusBadRequest, "缺少必要参数", nil)
		return
	}

	data, err := ctrl.commentService.GetComments(targetType, targetID, page, limit)
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "获取评论失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

func (ctrl *Controllers) PostComment(c *gin.Context) {
	// 从认证中间件获取用户ID
	userID, exists := c.Get("userID")
	if !exists {
		ctrl.sendError(c, http.StatusUnauthorized, "请先登录", nil)
		return
	}

	var req models.CommentCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	data, err := ctrl.commentService.CreateComment(userID.(uint64), &req)
	if err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "发布评论失败", err)
		return
	}

	c.JSON(http.StatusCreated, models.APIResponse{
		Code:    201,
		Message: "评论发布成功",
		Data:    data,
	})
}

// --- Ranking Handlers ---

func (ctrl *Controllers) GetTagRanking(c *gin.Context) {
	rankType := c.DefaultQuery("type", "hot")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	data, err := ctrl.tagService.GetTagRanking(rankType, page, limit)
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "获取标签排行榜失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

func (ctrl *Controllers) GetActorRanking(c *gin.Context) {
	rankType := c.DefaultQuery("type", "total")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	data, err := ctrl.actorService.GetActorRanking(rankType, page, limit)
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "获取演员排行榜失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

// --- Tag Handlers ---

func (ctrl *Controllers) GetTags(c *gin.Context) {
	data, err := ctrl.tagService.GetHotTags()
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "获取热门标签失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

func (ctrl *Controllers) GetTagByName(c *gin.Context) {
	tagName := c.Param("tagName")

	data, err := ctrl.tagService.GetTagDetail(tagName)
	if err != nil {
		ctrl.sendError(c, http.StatusNotFound, "标签不存在", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

func (ctrl *Controllers) GetTagDramas(c *gin.Context) {
	tagName := c.Param("tagName")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	data, err := ctrl.tagService.GetTagDramas(tagName, page, limit)
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "获取标签下的短剧失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

// --- User Handlers ---

func (ctrl *Controllers) GetUserFollows(c *gin.Context) {
	userID := ctrl.getUserID(c)
	if userID == 0 {
		ctrl.sendError(c, http.StatusUnauthorized, "请先登录", nil)
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	data, err := ctrl.actorService.GetFollowedActors(userID, page, limit)
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "获取关注列表失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

// --- Payment Handlers ---

func (ctrl *Controllers) UnlockPayment(c *gin.Context) {
	userID := ctrl.getUserID(c)
	if userID == 0 {
		ctrl.sendError(c, http.StatusUnauthorized, "请先登录", nil)
		return
	}

	var req models.PaymentUnlockRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	data, err := ctrl.paymentService.UnlockContent(userID, &req)
	if err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "解锁失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

func (ctrl *Controllers) GetPaymentStatus(c *gin.Context) {
	userID := ctrl.getUserID(c)
	if userID == 0 {
		ctrl.sendError(c, http.StatusUnauthorized, "请先登录", nil)
		return
	}

	contentType := c.Query("contentType")
	contentID := c.Query("contentId")

	if contentType == "" || contentID == "" {
		ctrl.sendError(c, http.StatusBadRequest, "缺少必要参数", nil)
		return
	}

	data, err := ctrl.paymentService.GetPaymentStatus(userID, contentType, contentID)
	if err != nil {
		ctrl.sendError(c, http.StatusInternalServerError, "查询付费状态失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

// --- Auth Handlers ---

// WechatLogin 微信登录
func (ctrl *Controllers) WechatLogin(c *gin.Context) {
	var req models.WechatLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	data, err := ctrl.wechatService.WechatLogin(&req)
	if err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "登录失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

// DecryptPhoneNumber 解密微信手机号
func (ctrl *Controllers) DecryptPhoneNumber(c *gin.Context) {
	userID := ctrl.getUserID(c)
	if userID == 0 {
		ctrl.sendError(c, http.StatusUnauthorized, "请先登录", nil)
		return
	}

	var req models.WechatPhoneRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	data, err := ctrl.wechatService.DecryptPhoneNumber(userID, &req)
	if err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "获取手机号失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}

// UpdateUserProfile 更新用户信息
func (ctrl *Controllers) UpdateUserProfile(c *gin.Context) {
	userID := ctrl.getUserID(c)
	if userID == 0 {
		ctrl.sendError(c, http.StatusUnauthorized, "请先登录", nil)
		return
	}

	var req models.UserProfileUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	data, err := ctrl.userService.UpdateUserProfile(userID, &req)
	if err != nil {
		ctrl.sendError(c, http.StatusBadRequest, "更新用户信息失败", err)
		return
	}

	ctrl.sendSuccess(c, data)
}
