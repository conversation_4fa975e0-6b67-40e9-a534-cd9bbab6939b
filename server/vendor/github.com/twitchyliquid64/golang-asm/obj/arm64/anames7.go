// Copyright 2015 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package arm64

// This order should be strictly consistent to that in a.out.go
var cnames7 = []string{
	"NONE",
	"REG",
	"RSP",
	"FREG",
	"VREG",
	"PAIR",
	"SHIFT",
	"EXTREG",
	"SPR",
	"COND",
	"ARNG",
	"<PERSON>LE<PERSON>",
	"LIST",
	"ZCON",
	"ABCON0",
	"ADDCON0",
	"ABCON",
	"AMCON",
	"ADDCON",
	"MBCON",
	"MOVCON",
	"BITCON",
	"ADDCON2",
	"LCON",
	"MOVCON2",
	"MOVCON3",
	"VC<PERSON>",
	"FCON",
	"<PERSON><PERSON>AD<PERSON>",
	"AACON",
	"AACON2",
	"LACON",
	"AECON",
	"SBRA",
	"LBRA",
	"ZAUTO",
	"NSAUTO_8",
	"NSAUTO_4",
	"<PERSON>UT<PERSON>",
	"<PERSON><PERSON><PERSON><PERSON>",
	"NAUTO4K",
	"PSAUTO_8",
	"PSAUTO_4",
	"<PERSON>AUT<PERSON>",
	"<PERSON>AUT<PERSON>",
	"UAUTO4K_8",
	"UAUTO4K_4",
	"UAUTO4K_2",
	"UAUTO4K",
	"UAUTO8K_8",
	"UAUTO8K_4",
	"UAUTO8K",
	"UAUTO16K_8",
	"UAUTO16K",
	"UAUTO32K",
	"LAUTO",
	"SEXT1",
	"SEXT2",
	"SEXT4",
	"SEXT8",
	"SEXT16",
	"LEXT",
	"ZOREG",
	"NSOREG_8",
	"NSOREG_4",
	"NSOREG",
	"NPOREG",
	"NOREG4K",
	"PSOREG_8",
	"PSOREG_4",
	"PSOREG",
	"PPOREG",
	"UOREG4K_8",
	"UOREG4K_4",
	"UOREG4K_2",
	"UOREG4K",
	"UOREG8K_8",
	"UOREG8K_4",
	"UOREG8K",
	"UOREG16K_8",
	"UOREG16K",
	"UOREG32K",
	"LOREG",
	"ADDR",
	"GOTADDR",
	"TLS_LE",
	"TLS_IE",
	"ROFF",
	"GOK",
	"TEXTSIZE",
	"NCLASS",
}
