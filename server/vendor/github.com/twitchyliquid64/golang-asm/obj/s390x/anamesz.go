// Copyright 2016 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package s390x

var cnamesz = []string{
	"NONE",
	"REG",
	"FREG",
	"VREG",
	"AREG",
	"Z<PERSON><PERSON>",
	"<PERSON><PERSON>",
	"<PERSON><PERSON>",
	"AD<PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"LCON",
	"<PERSON>ON",
	"SACON",
	"LACON",
	"D<PERSON>ON",
	"SBRA",
	"LBRA",
	"<PERSON>UT<PERSON>",
	"LAUTO",
	"ZOREG",
	"SOREG",
	"LOREG",
	"TLS_LE",
	"TLS_IE",
	"GOK",
	"ADDR",
	"SYMAD<PERSON>",
	"<PERSON><PERSON><PERSON><PERSON>",
	"TEXTS<PERSON>Z<PERSON>",
	"ANY",
	"<PERSON>LA<PERSON>",
}
