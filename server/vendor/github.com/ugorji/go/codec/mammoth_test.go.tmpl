//go:build !codec.notmammoth

// Copyright (c) 2012-2020 Ugorji Nwoke. All rights reserved.
// Use of this source code is governed by a MIT license found in the LICENSE file.

// Code generated from mammoth_test.go.tmpl - DO NOT EDIT.

package codec

import "testing"
import "fmt"
import "reflect"

// TestMammoth has all the different paths optimized in fastpath
// It has all the primitives, slices and maps.
// 
// For each of those types, it has a pointer and a non-pointer field.

func init() { _ = fmt.Printf } // so we can include fmt as needed

type TestMammoth struct {

{{range .Values }}{{if .Primitive -}}
{{ .MethodNamePfx "F" true }} {{ .Primitive }}
{{ .MethodNamePfx "Fptr" true }} *{{ .Primitive }}
{{end}}{{end}}

{{range .Values }}{{if not .Primitive }}{{if not .MapKey -}}
{{ .MethodNamePfx "F" false }} []{{ .Elem }}
{{ .MethodNamePfx "Fptr" false }} *[]{{ .Elem }}
{{ .MethodNamePfx "Farr4" false }} [4]{{ .Elem }}
{{end}}{{end}}{{end}}

{{range .Values }}{{if not .Primitive }}{{if .MapKey -}}
{{ .MethodNamePfx "F" false }} map[{{ .MapKey }}]{{ .Elem }}
{{ .MethodNamePfx "Fptr" false }} *map[{{ .MapKey }}]{{ .Elem }}
{{end}}{{end}}{{end}}

}

// -----------

// Increase codecoverage by covering all the codecgen paths, in fastpath ....
//
// Note: even though this is built based on fastpath, we will run these tests
// in all modes, including notfastpath, etc.
//
// Add test file for creating a mammoth generated file as _mammoth_generated.go
//
// Now, add some types:
//  - some that implement BinaryMarshal, TextMarshal, JSONMarshal, and one that implements none of it
//  - create a wrapper type that includes TestMammoth2, with it in slices, and maps, and the custom types
//  - this wrapper object is what we work encode/decode (so that the codecgen methods are called)

type testMammoth2Binary uint64
func (x testMammoth2Binary) MarshalBinary() (data []byte, err error) {
data = make([]byte, 8)
bigenstd.PutUint64(data, uint64(x))
return
}
func (x *testMammoth2Binary) UnmarshalBinary(data []byte) (err error) {
*x = testMammoth2Binary(bigenstd.Uint64(data))
return
}

type testMammoth2Text uint64
func (x testMammoth2Text) MarshalText() (data []byte, err error) {
data = []byte(fmt.Sprintf("%b", uint64(x)))
return
}
func (x *testMammoth2Text) UnmarshalText(data []byte) (err error) {
_, err = fmt.Sscanf(string(data), "%b", (*uint64)(x))
return
}

type testMammoth2Json uint64
func (x testMammoth2Json) MarshalJSON() (data []byte, err error) {
data = []byte(fmt.Sprintf("%v", uint64(x)))
return
}
func (x *testMammoth2Json) UnmarshalJSON(data []byte) (err error) {
_, err = fmt.Sscanf(string(data), "%v", (*uint64)(x))
return
}

type testMammoth2Basic [4]uint64

type TestMammoth2Wrapper struct {
	V TestMammoth
	T testMammoth2Text
	B testMammoth2Binary
	J testMammoth2Json
	C testMammoth2Basic
	M map[testMammoth2Basic]TestMammoth
	L []TestMammoth
	A [4]int64
	
  	Tcomplex128 complex128
	Tcomplex64 complex64
	Tbytes []uint8
	Tpbytes *[]uint8
}

// -----------

{{range .Values }}{{if not .Primitive }}{{if not .MapKey -}}
type {{ .MethodNamePfx "typMbs" false }} []{{ .Elem }}
func (_ {{ .MethodNamePfx "typMbs" false }}) MapBySlice() { }
{{end}}{{end}}{{end}}

{{range .Values }}{{if not .Primitive }}{{if .MapKey -}}
type {{ .MethodNamePfx "typMap" false }} map[{{ .MapKey }}]{{ .Elem }}
{{end}}{{end}}{{end}}

func __doTestMammothSlices(t *testing.T, h Handle) {
{{range $i, $e := .Values }}{{if not .Primitive }}{{if not .MapKey -}}
    var v{{$i}}va [8]{{ .Elem }}
    for _, v := range [][]{{ .Elem }}{ nil, {}, { {{ nonzerocmd .Elem }}, {{ zerocmd .Elem }}, {{ zerocmd .Elem }}, {{ nonzerocmd .Elem }} } } {
		{{/*
    // fmt.Printf(">>>> running mammoth slice v{{$i}}: %v\n", v)
    //   - encode value to some []byte
    //   - decode into a length-wise-equal []byte
    //   - check if equal to initial slice
    //   - encode ptr to the value
    //   - check if encode bytes are same
    //   - decode into ptrs to: nil, then 1-elem slice, equal-length, then large len slice
    //   - decode into non-addressable slice of equal length, then larger len 
    //   - for each decode, compare elem-by-elem to the original slice
    //   - 
    //   - rinse and repeat for a MapBySlice version
    //   - 
    */ -}}
		var v{{$i}}v1, v{{$i}}v2 []{{ .Elem }}
		var bs{{$i}} []byte
		v{{$i}}v1 = v
		bs{{$i}} = testMarshalErr(v{{$i}}v1, h, t, "enc-slice-v{{$i}}")
		if v == nil {
			v{{$i}}v2 = make([]{{ .Elem }}, 2)
			testUnmarshalErr(v{{$i}}v2, bs{{$i}}, h, t, "dec-slice-v{{$i}}")
			testDeepEqualErr(v{{$i}}v2[0], v{{$i}}v2[1], t, "equal-slice-v{{$i}}") // should not change
			testDeepEqualErr(len(v{{$i}}v2), 2, t, "equal-slice-v{{$i}}") // should not change
			v{{$i}}v2 = make([]{{ .Elem }}, 2)
			testUnmarshalErr(reflect.ValueOf(v{{$i}}v2), bs{{$i}}, h, t, "dec-slice-v{{$i}}-noaddr") // non-addressable value
			testDeepEqualErr(v{{$i}}v2[0], v{{$i}}v2[1], t, "equal-slice-v{{$i}}-noaddr") // should not change
			testDeepEqualErr(len(v{{$i}}v2), 2, t, "equal-slice-v{{$i}}") // should not change
		} else {
			v{{$i}}v2 = make([]{{ .Elem }}, len(v))
			testUnmarshalErr(v{{$i}}v2, bs{{$i}}, h, t, "dec-slice-v{{$i}}")
			testDeepEqualErrHandle(v{{$i}}v1, v{{$i}}v2, h, t, "equal-slice-v{{$i}}")
			v{{$i}}v2 = make([]{{ .Elem }}, len(v))
			testUnmarshalErr(reflect.ValueOf(v{{$i}}v2), bs{{$i}}, h, t, "dec-slice-v{{$i}}-noaddr") // non-addressable value
			testDeepEqualErrHandle(v{{$i}}v1, v{{$i}}v2, h, t, "equal-slice-v{{$i}}-noaddr")
		}
		testReleaseBytes(bs{{$i}})
		// ...
		bs{{$i}} = testMarshalErr(&v{{$i}}v1, h, t, "enc-slice-v{{$i}}-p")
		v{{$i}}v2 = nil
		testUnmarshalErr(&v{{$i}}v2, bs{{$i}}, h, t, "dec-slice-v{{$i}}-p")
		testDeepEqualErrHandle(v{{$i}}v1, v{{$i}}v2, h, t, "equal-slice-v{{$i}}-p")
		v{{$i}}va = [8]{{ .Elem }}{} // clear the array
		testUnmarshalErr(&v{{$i}}va, bs{{$i}}, h, t, "dec-array-v{{$i}}-p-1")
		if v{{$i}}v1 == nil && v{{$i}}v2 == nil { v{{$i}}v2 = []{{ .Elem }}{} } // so we can compare to zero len slice below
		testDeepEqualErrHandle(v{{$i}}va[:len(v{{$i}}v2)], v{{$i}}v2, h, t, "equal-array-v{{$i}}-p-1")
		v{{$i}}va = [8]{{ .Elem }}{} // clear the array		
		v{{$i}}v2 = v{{$i}}va[:1:1]
		testUnmarshalErr(&v{{$i}}v2, bs{{$i}}, h, t, "dec-slice-v{{$i}}-p-1")
		testDeepEqualErrHandle(v{{$i}}v1, v{{$i}}v2, h, t, "equal-slice-v{{$i}}-p-1")
		v{{$i}}va = [8]{{ .Elem }}{} // clear the array
		v{{$i}}v2 = v{{$i}}va[:len(v{{$i}}v1):len(v{{$i}}v1)]
		testUnmarshalErr(&v{{$i}}v2, bs{{$i}}, h, t, "dec-slice-v{{$i}}-p-len")
		testDeepEqualErrHandle(v{{$i}}v1, v{{$i}}v2, h, t, "equal-slice-v{{$i}}-p-len")
		v{{$i}}va = [8]{{ .Elem }}{} // clear the array
		v{{$i}}v2 = v{{$i}}va[:]
		testUnmarshalErr(&v{{$i}}v2, bs{{$i}}, h, t, "dec-slice-v{{$i}}-p-cap")
		testDeepEqualErrHandle(v{{$i}}v1, v{{$i}}v2, h, t, "equal-slice-v{{$i}}-p-cap")
		if len(v{{$i}}v1) > 1 {
			v{{$i}}va = [8]{{ .Elem }}{} // clear the array
			testUnmarshalErr((&v{{$i}}va)[:len(v{{$i}}v1)], bs{{$i}}, h, t, "dec-slice-v{{$i}}-p-len-noaddr")
			testDeepEqualErrHandle(v{{$i}}v1, v{{$i}}va[:len(v{{$i}}v1)], h, t, "equal-slice-v{{$i}}-p-len-noaddr")
			v{{$i}}va = [8]{{ .Elem }}{} // clear the array
			testUnmarshalErr((&v{{$i}}va)[:], bs{{$i}}, h, t, "dec-slice-v{{$i}}-p-cap-noaddr")
			testDeepEqualErrHandle(v{{$i}}v1, v{{$i}}va[:len(v{{$i}}v1)], h, t, "equal-slice-v{{$i}}-p-cap-noaddr")
		}
		testReleaseBytes(bs{{$i}})
		// ...
		var v{{$i}}v3, v{{$i}}v4 {{ .MethodNamePfx "typMbs" false }}
		v{{$i}}v2 = nil
		if v != nil { v{{$i}}v2 = make([]{{ .Elem }}, len(v)) }
		v{{$i}}v3 = {{ .MethodNamePfx "typMbs" false }}(v{{$i}}v1)
		v{{$i}}v4 = {{ .MethodNamePfx "typMbs" false }}(v{{$i}}v2)
		if v != nil {
			bs{{$i}} = testMarshalErr(v{{$i}}v3, h, t, "enc-slice-v{{$i}}-custom")
			testUnmarshalErr(v{{$i}}v4, bs{{$i}}, h, t, "dec-slice-v{{$i}}-custom")
			testDeepEqualErrHandle(v{{$i}}v3, v{{$i}}v4, h, t, "equal-slice-v{{$i}}-custom")
			testReleaseBytes(bs{{$i}})
		}
		bs{{$i}} = testMarshalErr(&v{{$i}}v3, h, t, "enc-slice-v{{$i}}-custom-p")
		v{{$i}}v2 = nil
		v{{$i}}v4 = {{ .MethodNamePfx "typMbs" false }}(v{{$i}}v2)
		testUnmarshalErr(&v{{$i}}v4, bs{{$i}}, h, t, "dec-slice-v{{$i}}-custom-p")
		testDeepEqualErrHandle(v{{$i}}v3, v{{$i}}v4, h, t, "equal-slice-v{{$i}}-custom-p")
		testReleaseBytes(bs{{$i}})
    }
{{end}}{{end}}{{end}}
}

func __doTestMammothMaps(t *testing.T, h Handle) {
{{range $i, $e := .Values }}{{if not .Primitive }}{{if .MapKey -}}
    for _, v := range []map[{{ .MapKey }}]{{ .Elem }}{ nil, {}, { {{ nonzerocmd .MapKey }}:{{ zerocmd .Elem }} {{if ne "bool" .MapKey}}, {{ nonzerocmd .MapKey }}:{{ nonzerocmd .Elem }} {{end}} } } {
		{{/* // fmt.Printf(">>>> running mammoth map v{{$i}}: %v\n", v) */ -}}
		var v{{$i}}v1, v{{$i}}v2 map[{{ .MapKey }}]{{ .Elem }}
		var bs{{$i}} []byte
		v{{$i}}v1 = v
		bs{{$i}} = testMarshalErr(v{{$i}}v1, h, t, "enc-map-v{{$i}}")
		if v != nil {
			v{{$i}}v2 = make(map[{{ .MapKey }}]{{ .Elem }}, len(v)) // reset map
			testUnmarshalErr(v{{$i}}v2, bs{{$i}}, h, t, "dec-map-v{{$i}}")
			testDeepEqualErrHandle(v{{$i}}v1, v{{$i}}v2, h, t, "equal-map-v{{$i}}")
			v{{$i}}v2 = make(map[{{ .MapKey }}]{{ .Elem }}, len(v)) // reset map
			testUnmarshalErr(reflect.ValueOf(v{{$i}}v2), bs{{$i}}, h, t, "dec-map-v{{$i}}-noaddr") // decode into non-addressable map value
			testDeepEqualErrHandle(v{{$i}}v1, v{{$i}}v2, h, t, "equal-map-v{{$i}}-noaddr")
		}
		if v == nil { v{{$i}}v2 = nil } else { v{{$i}}v2 = make(map[{{ .MapKey }}]{{ .Elem }}, len(v)) } // reset map
		testUnmarshalErr(&v{{$i}}v2, bs{{$i}}, h, t, "dec-map-v{{$i}}-p-len")
		testDeepEqualErrHandle(v{{$i}}v1, v{{$i}}v2, h, t, "equal-map-v{{$i}}-p-len")
		testReleaseBytes(bs{{$i}})
		bs{{$i}} = testMarshalErr(&v{{$i}}v1, h, t, "enc-map-v{{$i}}-p")
		v{{$i}}v2 = nil
		testUnmarshalErr(&v{{$i}}v2, bs{{$i}}, h, t, "dec-map-v{{$i}}-p-nil")
		testDeepEqualErrHandle(v{{$i}}v1, v{{$i}}v2, h, t, "equal-map-v{{$i}}-p-nil")
		testReleaseBytes(bs{{$i}})
		// ...
		if v == nil { v{{$i}}v2 = nil } else { v{{$i}}v2 = make(map[{{ .MapKey }}]{{ .Elem }}, len(v)) } // reset map
		var v{{$i}}v3, v{{$i}}v4 {{ .MethodNamePfx "typMap" false }}
		v{{$i}}v3 = {{ .MethodNamePfx "typMap" false }}(v{{$i}}v1)
		v{{$i}}v4 = {{ .MethodNamePfx "typMap" false }}(v{{$i}}v2)
		if v != nil {
			bs{{$i}} = testMarshalErr(v{{$i}}v3, h, t, "enc-map-v{{$i}}-custom")
			testUnmarshalErr(v{{$i}}v4, bs{{$i}}, h, t, "dec-map-v{{$i}}-p-len")
			testDeepEqualErrHandle(v{{$i}}v3, v{{$i}}v4, h, t, "equal-map-v{{$i}}-p-len")
			testReleaseBytes(bs{{$i}})
		}
		type s{{$i}}T struct {
			M map[{{ .MapKey }}]{{ .Elem }}
			Mp *map[{{ .MapKey }}]{{ .Elem }}
		}
		var m{{$i}}v99 = map[{{ .MapKey }}]{{ .Elem }}{
			{{ zerocmd .MapKey }}: {{ zerocmd .Elem }},
			{{ nonzerocmd .MapKey }}:{{ nonzerocmd .Elem }},
		}
		var s{{$i}}v1, s{{$i}}v2 s{{$i}}T
		bs{{$i}} = testMarshalErr(s{{$i}}v1, h, t, "enc-map-v{{$i}}-custom")
		testUnmarshalErr(&s{{$i}}v2, bs{{$i}}, h, t, "dec-map-v{{$i}}-p-len")
		testDeepEqualErrHandle(s{{$i}}v1, s{{$i}}v2, h, t, "equal-map-v{{$i}}-p-len")
		testReleaseBytes(bs{{$i}})
		s{{$i}}v2 = s{{$i}}T{}
		s{{$i}}v1.M = m{{$i}}v99
		bs{{$i}} = testMarshalErr(s{{$i}}v1, h, t, "enc-map-v{{$i}}-custom")
		testUnmarshalErr(&s{{$i}}v2, bs{{$i}}, h, t, "dec-map-v{{$i}}-p-len")
		testDeepEqualErrHandle(s{{$i}}v1, s{{$i}}v2, h, t, "equal-map-v{{$i}}-p-len")
		testReleaseBytes(bs{{$i}})
		s{{$i}}v2 = s{{$i}}T{}
		s{{$i}}v1.Mp = &m{{$i}}v99
		bs{{$i}} = testMarshalErr(s{{$i}}v1, h, t, "enc-map-v{{$i}}-custom")
		testUnmarshalErr(&s{{$i}}v2, bs{{$i}}, h, t, "dec-map-v{{$i}}-p-len")
		testDeepEqualErrHandle(s{{$i}}v1, s{{$i}}v2, h, t, "equal-map-v{{$i}}-p-len")
		testReleaseBytes(bs{{$i}})	
    }
{{end}}{{end}}{{end}}

}

func doTestMammothMapsAndSlices(t *testing.T, h Handle) {
	defer testSetup(t, &h)()
	if mh, ok := h.(*MsgpackHandle); ok {
		defer func(b bool) { mh.RawToString = b }(mh.RawToString)
		mh.RawToString = true
	}
	__doTestMammothSlices(t, h)
	__doTestMammothMaps(t, h)
}

func doTestMammoth(t *testing.T, h Handle) {
	defer testSetup(t, &h)()
	if mh, ok := h.(*MsgpackHandle); ok {
		defer func(b bool) { mh.RawToString = b }(mh.RawToString)
		mh.RawToString = true
	}

	name := h.Name()
	var b []byte

	var m, m2 TestMammoth
	testRandomFillRV(reflect.ValueOf(&m).Elem())
	b = testMarshalErr(&m, h, t, "mammoth-"+name)

	testUnmarshalErr(&m2, b, h, t, "mammoth-"+name)
	testDeepEqualErrHandle(&m, &m2, h, t, "mammoth-"+name)
	testReleaseBytes(b)

	if testing.Short() {
		t.Skipf("skipping rest of mammoth test in -short mode")
	}

	var mm, mm2 TestMammoth2Wrapper
	testRandomFillRV(reflect.ValueOf(&mm).Elem())
	b = testMarshalErr(&mm, h, t, "mammoth2-"+name)
	// os.Stderr.Write([]byte("\n\n\n\n" + string(b) + "\n\n\n\n"))
	testUnmarshalErr(&mm2, b, h, t, "mammoth2-"+name)
	testDeepEqualErrHandle(&mm, &mm2, h, t, "mammoth2-"+name)
	// testMammoth2(t, name, h)
	testReleaseBytes(b)
}

{{range $i, $e := .Formats -}}
func Test{{ . }}Mammoth(t *testing.T) {
	doTestMammoth(t, test{{ . }}H)
}
{{end}}
{{range $i, $e := .Formats -}}
func Test{{ . }}MammothMapsAndSlices(t *testing.T) {
	doTestMammothMapsAndSlices(t, test{{ . }}H)
}
{{end}}
