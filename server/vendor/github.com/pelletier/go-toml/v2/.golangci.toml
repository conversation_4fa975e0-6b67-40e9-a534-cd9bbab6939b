[service]
golangci-lint-version =  "1.39.0"

[linters-settings.wsl]
allow-assign-and-anything = true

[linters-settings.exhaustive]
default-signifies-exhaustive = true

[linters]
disable-all = true
enable = [
    "asciicheck",
    "bodyclose",
    "cyclop",
    "deadcode",
    "depguard",
    "dogsled",
    "dupl",
    "durationcheck",
    "errcheck",
    "errorlint",
    "exhaustive",
    # "exhaustivestruct",
    "exportloopref",
    "forbidigo",
    # "forcetypeassert",
    "funlen",
    "gci",
    # "gochecknoglobals",
    "gochecknoinits",
    "gocognit",
    "goconst",
    "gocritic",
    "gocyclo",
    "godot",
    "godox",
    # "goerr113",
    "gofmt",
    "gofumpt",
    "goheader",
    "goimports",
    "golint",
    "gomnd",
    # "gomoddirectives",
    "gomodguard",
    "goprintffuncname",
    "gosec",
    "gosimple",
    "govet",
    # "ifshort",
    "importas",
    "ineffassign",
    "lll",
    "makezero",
    "misspell",
    "nakedret",
    "nestif",
    "nilerr",
    # "nlreturn",
    "noctx",
    "nolintlint",
    #"paralleltest",
    "prealloc",
    "predeclared",
    "revive",
    "rowserrcheck",
    "sqlclosecheck",
    "staticcheck",
    "structcheck",
    "stylecheck",
    # "testpackage",
    "thelper",
    "tparallel",
    "typecheck",
    "unconvert",
    "unparam",
    "unused",
    "varcheck",
    "wastedassign",
    "whitespace",
    # "wrapcheck",
    # "wsl"
]
