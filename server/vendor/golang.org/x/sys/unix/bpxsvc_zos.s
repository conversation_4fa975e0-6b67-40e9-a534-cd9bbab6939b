// Copyright 2024 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

#include "go_asm.h"
#include "textflag.h"

// function to call USS assembly language services
//
// doc: https://www.ibm.com/support/knowledgecenter/en/SSLTBW_3.1.0/com.ibm.zos.v3r1.bpxb100/bit64env.htm
//
//   arg1 unsafe.Pointer array that ressembles an OS PLIST
//
//   arg2 function offset as in
//       doc: https://www.ibm.com/support/knowledgecenter/en/SSLTBW_3.1.0/com.ibm.zos.v3r1.bpxb100/bpx2cr_List_of_offsets.htm
//
// func bpxcall(plist []unsafe.Pointer, bpx_offset int64)

TEXT ·bpxcall(SB), NOSPLIT|NOFRAME, $0
	MOVD  plist_base+0(FP), R1  // r1 points to plist
	MOVD  bpx_offset+24(FP), R2 // r2 offset to BPX vector table
	MOVD  R14, R7               // save r14
	MOVD  R15, R8               // save r15
	MOVWZ 16(R0), R9
	MOVWZ 544(R9), R9
	MOVWZ 24(R9), R9            // call vector in r9
	ADD   R2, R9                // add offset to vector table
	MOVWZ (R9), R9              // r9 points to entry point
	BYTE  $0x0D                 // BL R14,R9 --> basr r14,r9
	BYTE  $0xE9                 // clobbers 0,1,14,15
	MOVD  R8, R15               // restore 15
	JMP   R7                    // return via saved return address

//   func A2e(arr [] byte)
//   code page conversion from  819 to 1047
TEXT ·A2e(SB), NOSPLIT|NOFRAME, $0
	MOVD arg_base+0(FP), R2                        // pointer to arry of characters
	MOVD arg_len+8(FP), R3                         // count
	XOR  R0, R0
	XOR  R1, R1
	BYTE $0xA7; BYTE $0x15; BYTE $0x00; BYTE $0x82 // BRAS 1,(2+(256/2))

	// ASCII -> EBCDIC conversion table:
	BYTE $0x00; BYTE $0x01; BYTE $0x02; BYTE $0x03
	BYTE $0x37; BYTE $0x2d; BYTE $0x2e; BYTE $0x2f
	BYTE $0x16; BYTE $0x05; BYTE $0x15; BYTE $0x0b
	BYTE $0x0c; BYTE $0x0d; BYTE $0x0e; BYTE $0x0f
	BYTE $0x10; BYTE $0x11; BYTE $0x12; BYTE $0x13
	BYTE $0x3c; BYTE $0x3d; BYTE $0x32; BYTE $0x26
	BYTE $0x18; BYTE $0x19; BYTE $0x3f; BYTE $0x27
	BYTE $0x1c; BYTE $0x1d; BYTE $0x1e; BYTE $0x1f
	BYTE $0x40; BYTE $0x5a; BYTE $0x7f; BYTE $0x7b
	BYTE $0x5b; BYTE $0x6c; BYTE $0x50; BYTE $0x7d
	BYTE $0x4d; BYTE $0x5d; BYTE $0x5c; BYTE $0x4e
	BYTE $0x6b; BYTE $0x60; BYTE $0x4b; BYTE $0x61
	BYTE $0xf0; BYTE $0xf1; BYTE $0xf2; BYTE $0xf3
	BYTE $0xf4; BYTE $0xf5; BYTE $0xf6; BYTE $0xf7
	BYTE $0xf8; BYTE $0xf9; BYTE $0x7a; BYTE $0x5e
	BYTE $0x4c; BYTE $0x7e; BYTE $0x6e; BYTE $0x6f
	BYTE $0x7c; BYTE $0xc1; BYTE $0xc2; BYTE $0xc3
	BYTE $0xc4; BYTE $0xc5; BYTE $0xc6; BYTE $0xc7
	BYTE $0xc8; BYTE $0xc9; BYTE $0xd1; BYTE $0xd2
	BYTE $0xd3; BYTE $0xd4; BYTE $0xd5; BYTE $0xd6
	BYTE $0xd7; BYTE $0xd8; BYTE $0xd9; BYTE $0xe2
	BYTE $0xe3; BYTE $0xe4; BYTE $0xe5; BYTE $0xe6
	BYTE $0xe7; BYTE $0xe8; BYTE $0xe9; BYTE $0xad
	BYTE $0xe0; BYTE $0xbd; BYTE $0x5f; BYTE $0x6d
	BYTE $0x79; BYTE $0x81; BYTE $0x82; BYTE $0x83
	BYTE $0x84; BYTE $0x85; BYTE $0x86; BYTE $0x87
	BYTE $0x88; BYTE $0x89; BYTE $0x91; BYTE $0x92
	BYTE $0x93; BYTE $0x94; BYTE $0x95; BYTE $0x96
	BYTE $0x97; BYTE $0x98; BYTE $0x99; BYTE $0xa2
	BYTE $0xa3; BYTE $0xa4; BYTE $0xa5; BYTE $0xa6
	BYTE $0xa7; BYTE $0xa8; BYTE $0xa9; BYTE $0xc0
	BYTE $0x4f; BYTE $0xd0; BYTE $0xa1; BYTE $0x07
	BYTE $0x20; BYTE $0x21; BYTE $0x22; BYTE $0x23
	BYTE $0x24; BYTE $0x25; BYTE $0x06; BYTE $0x17
	BYTE $0x28; BYTE $0x29; BYTE $0x2a; BYTE $0x2b
	BYTE $0x2c; BYTE $0x09; BYTE $0x0a; BYTE $0x1b
	BYTE $0x30; BYTE $0x31; BYTE $0x1a; BYTE $0x33
	BYTE $0x34; BYTE $0x35; BYTE $0x36; BYTE $0x08
	BYTE $0x38; BYTE $0x39; BYTE $0x3a; BYTE $0x3b
	BYTE $0x04; BYTE $0x14; BYTE $0x3e; BYTE $0xff
	BYTE $0x41; BYTE $0xaa; BYTE $0x4a; BYTE $0xb1
	BYTE $0x9f; BYTE $0xb2; BYTE $0x6a; BYTE $0xb5
	BYTE $0xbb; BYTE $0xb4; BYTE $0x9a; BYTE $0x8a
	BYTE $0xb0; BYTE $0xca; BYTE $0xaf; BYTE $0xbc
	BYTE $0x90; BYTE $0x8f; BYTE $0xea; BYTE $0xfa
	BYTE $0xbe; BYTE $0xa0; BYTE $0xb6; BYTE $0xb3
	BYTE $0x9d; BYTE $0xda; BYTE $0x9b; BYTE $0x8b
	BYTE $0xb7; BYTE $0xb8; BYTE $0xb9; BYTE $0xab
	BYTE $0x64; BYTE $0x65; BYTE $0x62; BYTE $0x66
	BYTE $0x63; BYTE $0x67; BYTE $0x9e; BYTE $0x68
	BYTE $0x74; BYTE $0x71; BYTE $0x72; BYTE $0x73
	BYTE $0x78; BYTE $0x75; BYTE $0x76; BYTE $0x77
	BYTE $0xac; BYTE $0x69; BYTE $0xed; BYTE $0xee
	BYTE $0xeb; BYTE $0xef; BYTE $0xec; BYTE $0xbf
	BYTE $0x80; BYTE $0xfd; BYTE $0xfe; BYTE $0xfb
	BYTE $0xfc; BYTE $0xba; BYTE $0xae; BYTE $0x59
	BYTE $0x44; BYTE $0x45; BYTE $0x42; BYTE $0x46
	BYTE $0x43; BYTE $0x47; BYTE $0x9c; BYTE $0x48
	BYTE $0x54; BYTE $0x51; BYTE $0x52; BYTE $0x53
	BYTE $0x58; BYTE $0x55; BYTE $0x56; BYTE $0x57
	BYTE $0x8c; BYTE $0x49; BYTE $0xcd; BYTE $0xce
	BYTE $0xcb; BYTE $0xcf; BYTE $0xcc; BYTE $0xe1
	BYTE $0x70; BYTE $0xdd; BYTE $0xde; BYTE $0xdb
	BYTE $0xdc; BYTE $0x8d; BYTE $0x8e; BYTE $0xdf

retry:
	WORD $0xB9931022 // TROO 2,2,b'0001'
	BVS  retry
	RET

//   func e2a(arr [] byte)
//   code page conversion from  1047 to 819
TEXT ·E2a(SB), NOSPLIT|NOFRAME, $0
	MOVD arg_base+0(FP), R2                        // pointer to arry of characters
	MOVD arg_len+8(FP), R3                         // count
	XOR  R0, R0
	XOR  R1, R1
	BYTE $0xA7; BYTE $0x15; BYTE $0x00; BYTE $0x82 // BRAS 1,(2+(256/2))

	// EBCDIC -> ASCII conversion table:
	BYTE $0x00; BYTE $0x01; BYTE $0x02; BYTE $0x03
	BYTE $0x9c; BYTE $0x09; BYTE $0x86; BYTE $0x7f
	BYTE $0x97; BYTE $0x8d; BYTE $0x8e; BYTE $0x0b
	BYTE $0x0c; BYTE $0x0d; BYTE $0x0e; BYTE $0x0f
	BYTE $0x10; BYTE $0x11; BYTE $0x12; BYTE $0x13
	BYTE $0x9d; BYTE $0x0a; BYTE $0x08; BYTE $0x87
	BYTE $0x18; BYTE $0x19; BYTE $0x92; BYTE $0x8f
	BYTE $0x1c; BYTE $0x1d; BYTE $0x1e; BYTE $0x1f
	BYTE $0x80; BYTE $0x81; BYTE $0x82; BYTE $0x83
	BYTE $0x84; BYTE $0x85; BYTE $0x17; BYTE $0x1b
	BYTE $0x88; BYTE $0x89; BYTE $0x8a; BYTE $0x8b
	BYTE $0x8c; BYTE $0x05; BYTE $0x06; BYTE $0x07
	BYTE $0x90; BYTE $0x91; BYTE $0x16; BYTE $0x93
	BYTE $0x94; BYTE $0x95; BYTE $0x96; BYTE $0x04
	BYTE $0x98; BYTE $0x99; BYTE $0x9a; BYTE $0x9b
	BYTE $0x14; BYTE $0x15; BYTE $0x9e; BYTE $0x1a
	BYTE $0x20; BYTE $0xa0; BYTE $0xe2; BYTE $0xe4
	BYTE $0xe0; BYTE $0xe1; BYTE $0xe3; BYTE $0xe5
	BYTE $0xe7; BYTE $0xf1; BYTE $0xa2; BYTE $0x2e
	BYTE $0x3c; BYTE $0x28; BYTE $0x2b; BYTE $0x7c
	BYTE $0x26; BYTE $0xe9; BYTE $0xea; BYTE $0xeb
	BYTE $0xe8; BYTE $0xed; BYTE $0xee; BYTE $0xef
	BYTE $0xec; BYTE $0xdf; BYTE $0x21; BYTE $0x24
	BYTE $0x2a; BYTE $0x29; BYTE $0x3b; BYTE $0x5e
	BYTE $0x2d; BYTE $0x2f; BYTE $0xc2; BYTE $0xc4
	BYTE $0xc0; BYTE $0xc1; BYTE $0xc3; BYTE $0xc5
	BYTE $0xc7; BYTE $0xd1; BYTE $0xa6; BYTE $0x2c
	BYTE $0x25; BYTE $0x5f; BYTE $0x3e; BYTE $0x3f
	BYTE $0xf8; BYTE $0xc9; BYTE $0xca; BYTE $0xcb
	BYTE $0xc8; BYTE $0xcd; BYTE $0xce; BYTE $0xcf
	BYTE $0xcc; BYTE $0x60; BYTE $0x3a; BYTE $0x23
	BYTE $0x40; BYTE $0x27; BYTE $0x3d; BYTE $0x22
	BYTE $0xd8; BYTE $0x61; BYTE $0x62; BYTE $0x63
	BYTE $0x64; BYTE $0x65; BYTE $0x66; BYTE $0x67
	BYTE $0x68; BYTE $0x69; BYTE $0xab; BYTE $0xbb
	BYTE $0xf0; BYTE $0xfd; BYTE $0xfe; BYTE $0xb1
	BYTE $0xb0; BYTE $0x6a; BYTE $0x6b; BYTE $0x6c
	BYTE $0x6d; BYTE $0x6e; BYTE $0x6f; BYTE $0x70
	BYTE $0x71; BYTE $0x72; BYTE $0xaa; BYTE $0xba
	BYTE $0xe6; BYTE $0xb8; BYTE $0xc6; BYTE $0xa4
	BYTE $0xb5; BYTE $0x7e; BYTE $0x73; BYTE $0x74
	BYTE $0x75; BYTE $0x76; BYTE $0x77; BYTE $0x78
	BYTE $0x79; BYTE $0x7a; BYTE $0xa1; BYTE $0xbf
	BYTE $0xd0; BYTE $0x5b; BYTE $0xde; BYTE $0xae
	BYTE $0xac; BYTE $0xa3; BYTE $0xa5; BYTE $0xb7
	BYTE $0xa9; BYTE $0xa7; BYTE $0xb6; BYTE $0xbc
	BYTE $0xbd; BYTE $0xbe; BYTE $0xdd; BYTE $0xa8
	BYTE $0xaf; BYTE $0x5d; BYTE $0xb4; BYTE $0xd7
	BYTE $0x7b; BYTE $0x41; BYTE $0x42; BYTE $0x43
	BYTE $0x44; BYTE $0x45; BYTE $0x46; BYTE $0x47
	BYTE $0x48; BYTE $0x49; BYTE $0xad; BYTE $0xf4
	BYTE $0xf6; BYTE $0xf2; BYTE $0xf3; BYTE $0xf5
	BYTE $0x7d; BYTE $0x4a; BYTE $0x4b; BYTE $0x4c
	BYTE $0x4d; BYTE $0x4e; BYTE $0x4f; BYTE $0x50
	BYTE $0x51; BYTE $0x52; BYTE $0xb9; BYTE $0xfb
	BYTE $0xfc; BYTE $0xf9; BYTE $0xfa; BYTE $0xff
	BYTE $0x5c; BYTE $0xf7; BYTE $0x53; BYTE $0x54
	BYTE $0x55; BYTE $0x56; BYTE $0x57; BYTE $0x58
	BYTE $0x59; BYTE $0x5a; BYTE $0xb2; BYTE $0xd4
	BYTE $0xd6; BYTE $0xd2; BYTE $0xd3; BYTE $0xd5
	BYTE $0x30; BYTE $0x31; BYTE $0x32; BYTE $0x33
	BYTE $0x34; BYTE $0x35; BYTE $0x36; BYTE $0x37
	BYTE $0x38; BYTE $0x39; BYTE $0xb3; BYTE $0xdb
	BYTE $0xdc; BYTE $0xd9; BYTE $0xda; BYTE $0x9f

retry:
	WORD $0xB9931022 // TROO 2,2,b'0001'
	BVS  retry
	RET
