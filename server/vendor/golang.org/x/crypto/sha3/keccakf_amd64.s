// Code generated by command: go run keccakf_amd64_asm.go -out ../keccakf_amd64.s -pkg sha3. DO NOT EDIT.

//go:build amd64 && !purego && gc

// func keccakF1600(a *[25]uint64)
TEXT ·keccakF1600(SB), $200-8
	MOVQ a+0(FP), DI

	// Convert the user state into an internal state
	NOTQ 8(DI)
	NOTQ 16(DI)
	NOTQ 64(DI)
	NOTQ 96(DI)
	NOTQ 136(DI)
	NOTQ 160(DI)

	// Execute the KeccakF permutation
	MOVQ (DI), SI
	MOVQ 8(DI), BP
	MOVQ 32(DI), R15
	XORQ 40(DI), SI
	XORQ 48(DI), BP
	XORQ 72(DI), R15
	XORQ 80(DI), SI
	XORQ 88(DI), BP
	XORQ 112(DI), R15
	XORQ 120(DI), SI
	XORQ 128(DI), BP
	XORQ 152(DI), R15
	XORQ 160(DI), SI
	XORQ 168(DI), BP
	MOVQ 176(DI), DX
	MOVQ 184(DI), R8
	XORQ 192(DI), R15

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(DI), R12
	XORQ 56(DI), DX
	XORQ R15, BX
	XORQ 96(DI), R12
	XORQ 136(DI), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(DI), R13
	XORQ 64(DI), R8
	XORQ SI, CX
	XORQ 104(DI), R13
	XORQ 144(DI), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (DI), R10
	MOVQ 48(DI), R11
	XORQ R13, R9
	MOVQ 96(DI), R12
	MOVQ 144(DI), R13
	MOVQ 192(DI), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x0000000000000001, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (SP)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(SP)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(SP)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(SP)
	MOVQ R12, 8(SP)
	MOVQ R12, BP

	// Result g
	MOVQ 72(DI), R11
	XORQ R9, R11
	MOVQ 80(DI), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(DI), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(DI), R13
	MOVQ 176(DI), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(SP)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(SP)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(SP)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(SP)

	// Result k
	MOVQ 8(DI), R10
	MOVQ 56(DI), R11
	MOVQ 104(DI), R12
	MOVQ 152(DI), R13
	MOVQ 160(DI), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(SP)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(SP)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(SP)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(SP)
	XORQ R10, R15

	// Result m
	MOVQ 40(DI), R11
	XORQ BX, R11
	MOVQ 88(DI), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(DI), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(DI), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(DI), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(SP)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(SP)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(SP)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(SP)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(SP)
	XORQ R11, R15

	// Result s
	MOVQ 16(DI), R10
	MOVQ 64(DI), R11
	MOVQ 112(DI), R12
	XORQ DX, R10
	MOVQ 120(DI), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(DI), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(SP)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(SP)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(SP)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(SP)
	MOVQ R8, 184(SP)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(SP), R12
	XORQ 56(SP), DX
	XORQ R15, BX
	XORQ 96(SP), R12
	XORQ 136(SP), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(SP), R13
	XORQ 64(SP), R8
	XORQ SI, CX
	XORQ 104(SP), R13
	XORQ 144(SP), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (SP), R10
	MOVQ 48(SP), R11
	XORQ R13, R9
	MOVQ 96(SP), R12
	MOVQ 144(SP), R13
	MOVQ 192(SP), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x0000000000008082, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (DI)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(DI)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(DI)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(DI)
	MOVQ R12, 8(DI)
	MOVQ R12, BP

	// Result g
	MOVQ 72(SP), R11
	XORQ R9, R11
	MOVQ 80(SP), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(SP), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(SP), R13
	MOVQ 176(SP), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(DI)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(DI)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(DI)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(DI)

	// Result k
	MOVQ 8(SP), R10
	MOVQ 56(SP), R11
	MOVQ 104(SP), R12
	MOVQ 152(SP), R13
	MOVQ 160(SP), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(DI)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(DI)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(DI)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(DI)
	XORQ R10, R15

	// Result m
	MOVQ 40(SP), R11
	XORQ BX, R11
	MOVQ 88(SP), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(SP), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(SP), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(SP), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(DI)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(DI)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(DI)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(DI)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(DI)
	XORQ R11, R15

	// Result s
	MOVQ 16(SP), R10
	MOVQ 64(SP), R11
	MOVQ 112(SP), R12
	XORQ DX, R10
	MOVQ 120(SP), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(SP), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(DI)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(DI)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(DI)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(DI)
	MOVQ R8, 184(DI)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(DI), R12
	XORQ 56(DI), DX
	XORQ R15, BX
	XORQ 96(DI), R12
	XORQ 136(DI), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(DI), R13
	XORQ 64(DI), R8
	XORQ SI, CX
	XORQ 104(DI), R13
	XORQ 144(DI), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (DI), R10
	MOVQ 48(DI), R11
	XORQ R13, R9
	MOVQ 96(DI), R12
	MOVQ 144(DI), R13
	MOVQ 192(DI), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x800000000000808a, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (SP)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(SP)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(SP)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(SP)
	MOVQ R12, 8(SP)
	MOVQ R12, BP

	// Result g
	MOVQ 72(DI), R11
	XORQ R9, R11
	MOVQ 80(DI), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(DI), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(DI), R13
	MOVQ 176(DI), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(SP)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(SP)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(SP)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(SP)

	// Result k
	MOVQ 8(DI), R10
	MOVQ 56(DI), R11
	MOVQ 104(DI), R12
	MOVQ 152(DI), R13
	MOVQ 160(DI), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(SP)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(SP)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(SP)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(SP)
	XORQ R10, R15

	// Result m
	MOVQ 40(DI), R11
	XORQ BX, R11
	MOVQ 88(DI), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(DI), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(DI), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(DI), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(SP)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(SP)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(SP)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(SP)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(SP)
	XORQ R11, R15

	// Result s
	MOVQ 16(DI), R10
	MOVQ 64(DI), R11
	MOVQ 112(DI), R12
	XORQ DX, R10
	MOVQ 120(DI), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(DI), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(SP)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(SP)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(SP)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(SP)
	MOVQ R8, 184(SP)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(SP), R12
	XORQ 56(SP), DX
	XORQ R15, BX
	XORQ 96(SP), R12
	XORQ 136(SP), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(SP), R13
	XORQ 64(SP), R8
	XORQ SI, CX
	XORQ 104(SP), R13
	XORQ 144(SP), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (SP), R10
	MOVQ 48(SP), R11
	XORQ R13, R9
	MOVQ 96(SP), R12
	MOVQ 144(SP), R13
	MOVQ 192(SP), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x8000000080008000, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (DI)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(DI)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(DI)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(DI)
	MOVQ R12, 8(DI)
	MOVQ R12, BP

	// Result g
	MOVQ 72(SP), R11
	XORQ R9, R11
	MOVQ 80(SP), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(SP), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(SP), R13
	MOVQ 176(SP), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(DI)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(DI)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(DI)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(DI)

	// Result k
	MOVQ 8(SP), R10
	MOVQ 56(SP), R11
	MOVQ 104(SP), R12
	MOVQ 152(SP), R13
	MOVQ 160(SP), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(DI)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(DI)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(DI)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(DI)
	XORQ R10, R15

	// Result m
	MOVQ 40(SP), R11
	XORQ BX, R11
	MOVQ 88(SP), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(SP), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(SP), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(SP), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(DI)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(DI)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(DI)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(DI)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(DI)
	XORQ R11, R15

	// Result s
	MOVQ 16(SP), R10
	MOVQ 64(SP), R11
	MOVQ 112(SP), R12
	XORQ DX, R10
	MOVQ 120(SP), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(SP), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(DI)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(DI)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(DI)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(DI)
	MOVQ R8, 184(DI)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(DI), R12
	XORQ 56(DI), DX
	XORQ R15, BX
	XORQ 96(DI), R12
	XORQ 136(DI), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(DI), R13
	XORQ 64(DI), R8
	XORQ SI, CX
	XORQ 104(DI), R13
	XORQ 144(DI), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (DI), R10
	MOVQ 48(DI), R11
	XORQ R13, R9
	MOVQ 96(DI), R12
	MOVQ 144(DI), R13
	MOVQ 192(DI), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x000000000000808b, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (SP)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(SP)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(SP)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(SP)
	MOVQ R12, 8(SP)
	MOVQ R12, BP

	// Result g
	MOVQ 72(DI), R11
	XORQ R9, R11
	MOVQ 80(DI), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(DI), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(DI), R13
	MOVQ 176(DI), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(SP)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(SP)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(SP)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(SP)

	// Result k
	MOVQ 8(DI), R10
	MOVQ 56(DI), R11
	MOVQ 104(DI), R12
	MOVQ 152(DI), R13
	MOVQ 160(DI), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(SP)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(SP)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(SP)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(SP)
	XORQ R10, R15

	// Result m
	MOVQ 40(DI), R11
	XORQ BX, R11
	MOVQ 88(DI), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(DI), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(DI), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(DI), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(SP)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(SP)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(SP)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(SP)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(SP)
	XORQ R11, R15

	// Result s
	MOVQ 16(DI), R10
	MOVQ 64(DI), R11
	MOVQ 112(DI), R12
	XORQ DX, R10
	MOVQ 120(DI), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(DI), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(SP)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(SP)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(SP)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(SP)
	MOVQ R8, 184(SP)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(SP), R12
	XORQ 56(SP), DX
	XORQ R15, BX
	XORQ 96(SP), R12
	XORQ 136(SP), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(SP), R13
	XORQ 64(SP), R8
	XORQ SI, CX
	XORQ 104(SP), R13
	XORQ 144(SP), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (SP), R10
	MOVQ 48(SP), R11
	XORQ R13, R9
	MOVQ 96(SP), R12
	MOVQ 144(SP), R13
	MOVQ 192(SP), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x0000000080000001, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (DI)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(DI)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(DI)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(DI)
	MOVQ R12, 8(DI)
	MOVQ R12, BP

	// Result g
	MOVQ 72(SP), R11
	XORQ R9, R11
	MOVQ 80(SP), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(SP), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(SP), R13
	MOVQ 176(SP), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(DI)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(DI)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(DI)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(DI)

	// Result k
	MOVQ 8(SP), R10
	MOVQ 56(SP), R11
	MOVQ 104(SP), R12
	MOVQ 152(SP), R13
	MOVQ 160(SP), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(DI)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(DI)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(DI)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(DI)
	XORQ R10, R15

	// Result m
	MOVQ 40(SP), R11
	XORQ BX, R11
	MOVQ 88(SP), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(SP), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(SP), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(SP), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(DI)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(DI)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(DI)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(DI)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(DI)
	XORQ R11, R15

	// Result s
	MOVQ 16(SP), R10
	MOVQ 64(SP), R11
	MOVQ 112(SP), R12
	XORQ DX, R10
	MOVQ 120(SP), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(SP), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(DI)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(DI)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(DI)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(DI)
	MOVQ R8, 184(DI)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(DI), R12
	XORQ 56(DI), DX
	XORQ R15, BX
	XORQ 96(DI), R12
	XORQ 136(DI), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(DI), R13
	XORQ 64(DI), R8
	XORQ SI, CX
	XORQ 104(DI), R13
	XORQ 144(DI), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (DI), R10
	MOVQ 48(DI), R11
	XORQ R13, R9
	MOVQ 96(DI), R12
	MOVQ 144(DI), R13
	MOVQ 192(DI), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x8000000080008081, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (SP)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(SP)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(SP)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(SP)
	MOVQ R12, 8(SP)
	MOVQ R12, BP

	// Result g
	MOVQ 72(DI), R11
	XORQ R9, R11
	MOVQ 80(DI), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(DI), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(DI), R13
	MOVQ 176(DI), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(SP)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(SP)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(SP)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(SP)

	// Result k
	MOVQ 8(DI), R10
	MOVQ 56(DI), R11
	MOVQ 104(DI), R12
	MOVQ 152(DI), R13
	MOVQ 160(DI), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(SP)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(SP)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(SP)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(SP)
	XORQ R10, R15

	// Result m
	MOVQ 40(DI), R11
	XORQ BX, R11
	MOVQ 88(DI), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(DI), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(DI), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(DI), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(SP)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(SP)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(SP)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(SP)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(SP)
	XORQ R11, R15

	// Result s
	MOVQ 16(DI), R10
	MOVQ 64(DI), R11
	MOVQ 112(DI), R12
	XORQ DX, R10
	MOVQ 120(DI), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(DI), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(SP)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(SP)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(SP)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(SP)
	MOVQ R8, 184(SP)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(SP), R12
	XORQ 56(SP), DX
	XORQ R15, BX
	XORQ 96(SP), R12
	XORQ 136(SP), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(SP), R13
	XORQ 64(SP), R8
	XORQ SI, CX
	XORQ 104(SP), R13
	XORQ 144(SP), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (SP), R10
	MOVQ 48(SP), R11
	XORQ R13, R9
	MOVQ 96(SP), R12
	MOVQ 144(SP), R13
	MOVQ 192(SP), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x8000000000008009, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (DI)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(DI)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(DI)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(DI)
	MOVQ R12, 8(DI)
	MOVQ R12, BP

	// Result g
	MOVQ 72(SP), R11
	XORQ R9, R11
	MOVQ 80(SP), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(SP), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(SP), R13
	MOVQ 176(SP), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(DI)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(DI)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(DI)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(DI)

	// Result k
	MOVQ 8(SP), R10
	MOVQ 56(SP), R11
	MOVQ 104(SP), R12
	MOVQ 152(SP), R13
	MOVQ 160(SP), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(DI)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(DI)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(DI)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(DI)
	XORQ R10, R15

	// Result m
	MOVQ 40(SP), R11
	XORQ BX, R11
	MOVQ 88(SP), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(SP), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(SP), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(SP), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(DI)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(DI)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(DI)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(DI)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(DI)
	XORQ R11, R15

	// Result s
	MOVQ 16(SP), R10
	MOVQ 64(SP), R11
	MOVQ 112(SP), R12
	XORQ DX, R10
	MOVQ 120(SP), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(SP), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(DI)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(DI)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(DI)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(DI)
	MOVQ R8, 184(DI)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(DI), R12
	XORQ 56(DI), DX
	XORQ R15, BX
	XORQ 96(DI), R12
	XORQ 136(DI), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(DI), R13
	XORQ 64(DI), R8
	XORQ SI, CX
	XORQ 104(DI), R13
	XORQ 144(DI), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (DI), R10
	MOVQ 48(DI), R11
	XORQ R13, R9
	MOVQ 96(DI), R12
	MOVQ 144(DI), R13
	MOVQ 192(DI), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x000000000000008a, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (SP)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(SP)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(SP)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(SP)
	MOVQ R12, 8(SP)
	MOVQ R12, BP

	// Result g
	MOVQ 72(DI), R11
	XORQ R9, R11
	MOVQ 80(DI), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(DI), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(DI), R13
	MOVQ 176(DI), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(SP)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(SP)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(SP)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(SP)

	// Result k
	MOVQ 8(DI), R10
	MOVQ 56(DI), R11
	MOVQ 104(DI), R12
	MOVQ 152(DI), R13
	MOVQ 160(DI), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(SP)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(SP)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(SP)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(SP)
	XORQ R10, R15

	// Result m
	MOVQ 40(DI), R11
	XORQ BX, R11
	MOVQ 88(DI), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(DI), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(DI), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(DI), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(SP)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(SP)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(SP)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(SP)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(SP)
	XORQ R11, R15

	// Result s
	MOVQ 16(DI), R10
	MOVQ 64(DI), R11
	MOVQ 112(DI), R12
	XORQ DX, R10
	MOVQ 120(DI), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(DI), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(SP)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(SP)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(SP)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(SP)
	MOVQ R8, 184(SP)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(SP), R12
	XORQ 56(SP), DX
	XORQ R15, BX
	XORQ 96(SP), R12
	XORQ 136(SP), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(SP), R13
	XORQ 64(SP), R8
	XORQ SI, CX
	XORQ 104(SP), R13
	XORQ 144(SP), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (SP), R10
	MOVQ 48(SP), R11
	XORQ R13, R9
	MOVQ 96(SP), R12
	MOVQ 144(SP), R13
	MOVQ 192(SP), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x0000000000000088, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (DI)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(DI)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(DI)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(DI)
	MOVQ R12, 8(DI)
	MOVQ R12, BP

	// Result g
	MOVQ 72(SP), R11
	XORQ R9, R11
	MOVQ 80(SP), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(SP), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(SP), R13
	MOVQ 176(SP), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(DI)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(DI)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(DI)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(DI)

	// Result k
	MOVQ 8(SP), R10
	MOVQ 56(SP), R11
	MOVQ 104(SP), R12
	MOVQ 152(SP), R13
	MOVQ 160(SP), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(DI)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(DI)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(DI)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(DI)
	XORQ R10, R15

	// Result m
	MOVQ 40(SP), R11
	XORQ BX, R11
	MOVQ 88(SP), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(SP), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(SP), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(SP), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(DI)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(DI)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(DI)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(DI)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(DI)
	XORQ R11, R15

	// Result s
	MOVQ 16(SP), R10
	MOVQ 64(SP), R11
	MOVQ 112(SP), R12
	XORQ DX, R10
	MOVQ 120(SP), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(SP), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(DI)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(DI)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(DI)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(DI)
	MOVQ R8, 184(DI)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(DI), R12
	XORQ 56(DI), DX
	XORQ R15, BX
	XORQ 96(DI), R12
	XORQ 136(DI), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(DI), R13
	XORQ 64(DI), R8
	XORQ SI, CX
	XORQ 104(DI), R13
	XORQ 144(DI), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (DI), R10
	MOVQ 48(DI), R11
	XORQ R13, R9
	MOVQ 96(DI), R12
	MOVQ 144(DI), R13
	MOVQ 192(DI), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x0000000080008009, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (SP)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(SP)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(SP)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(SP)
	MOVQ R12, 8(SP)
	MOVQ R12, BP

	// Result g
	MOVQ 72(DI), R11
	XORQ R9, R11
	MOVQ 80(DI), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(DI), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(DI), R13
	MOVQ 176(DI), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(SP)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(SP)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(SP)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(SP)

	// Result k
	MOVQ 8(DI), R10
	MOVQ 56(DI), R11
	MOVQ 104(DI), R12
	MOVQ 152(DI), R13
	MOVQ 160(DI), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(SP)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(SP)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(SP)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(SP)
	XORQ R10, R15

	// Result m
	MOVQ 40(DI), R11
	XORQ BX, R11
	MOVQ 88(DI), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(DI), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(DI), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(DI), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(SP)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(SP)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(SP)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(SP)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(SP)
	XORQ R11, R15

	// Result s
	MOVQ 16(DI), R10
	MOVQ 64(DI), R11
	MOVQ 112(DI), R12
	XORQ DX, R10
	MOVQ 120(DI), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(DI), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(SP)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(SP)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(SP)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(SP)
	MOVQ R8, 184(SP)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(SP), R12
	XORQ 56(SP), DX
	XORQ R15, BX
	XORQ 96(SP), R12
	XORQ 136(SP), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(SP), R13
	XORQ 64(SP), R8
	XORQ SI, CX
	XORQ 104(SP), R13
	XORQ 144(SP), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (SP), R10
	MOVQ 48(SP), R11
	XORQ R13, R9
	MOVQ 96(SP), R12
	MOVQ 144(SP), R13
	MOVQ 192(SP), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x000000008000000a, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (DI)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(DI)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(DI)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(DI)
	MOVQ R12, 8(DI)
	MOVQ R12, BP

	// Result g
	MOVQ 72(SP), R11
	XORQ R9, R11
	MOVQ 80(SP), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(SP), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(SP), R13
	MOVQ 176(SP), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(DI)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(DI)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(DI)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(DI)

	// Result k
	MOVQ 8(SP), R10
	MOVQ 56(SP), R11
	MOVQ 104(SP), R12
	MOVQ 152(SP), R13
	MOVQ 160(SP), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(DI)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(DI)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(DI)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(DI)
	XORQ R10, R15

	// Result m
	MOVQ 40(SP), R11
	XORQ BX, R11
	MOVQ 88(SP), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(SP), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(SP), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(SP), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(DI)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(DI)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(DI)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(DI)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(DI)
	XORQ R11, R15

	// Result s
	MOVQ 16(SP), R10
	MOVQ 64(SP), R11
	MOVQ 112(SP), R12
	XORQ DX, R10
	MOVQ 120(SP), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(SP), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(DI)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(DI)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(DI)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(DI)
	MOVQ R8, 184(DI)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(DI), R12
	XORQ 56(DI), DX
	XORQ R15, BX
	XORQ 96(DI), R12
	XORQ 136(DI), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(DI), R13
	XORQ 64(DI), R8
	XORQ SI, CX
	XORQ 104(DI), R13
	XORQ 144(DI), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (DI), R10
	MOVQ 48(DI), R11
	XORQ R13, R9
	MOVQ 96(DI), R12
	MOVQ 144(DI), R13
	MOVQ 192(DI), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x000000008000808b, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (SP)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(SP)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(SP)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(SP)
	MOVQ R12, 8(SP)
	MOVQ R12, BP

	// Result g
	MOVQ 72(DI), R11
	XORQ R9, R11
	MOVQ 80(DI), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(DI), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(DI), R13
	MOVQ 176(DI), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(SP)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(SP)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(SP)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(SP)

	// Result k
	MOVQ 8(DI), R10
	MOVQ 56(DI), R11
	MOVQ 104(DI), R12
	MOVQ 152(DI), R13
	MOVQ 160(DI), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(SP)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(SP)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(SP)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(SP)
	XORQ R10, R15

	// Result m
	MOVQ 40(DI), R11
	XORQ BX, R11
	MOVQ 88(DI), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(DI), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(DI), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(DI), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(SP)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(SP)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(SP)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(SP)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(SP)
	XORQ R11, R15

	// Result s
	MOVQ 16(DI), R10
	MOVQ 64(DI), R11
	MOVQ 112(DI), R12
	XORQ DX, R10
	MOVQ 120(DI), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(DI), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(SP)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(SP)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(SP)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(SP)
	MOVQ R8, 184(SP)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(SP), R12
	XORQ 56(SP), DX
	XORQ R15, BX
	XORQ 96(SP), R12
	XORQ 136(SP), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(SP), R13
	XORQ 64(SP), R8
	XORQ SI, CX
	XORQ 104(SP), R13
	XORQ 144(SP), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (SP), R10
	MOVQ 48(SP), R11
	XORQ R13, R9
	MOVQ 96(SP), R12
	MOVQ 144(SP), R13
	MOVQ 192(SP), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x800000000000008b, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (DI)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(DI)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(DI)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(DI)
	MOVQ R12, 8(DI)
	MOVQ R12, BP

	// Result g
	MOVQ 72(SP), R11
	XORQ R9, R11
	MOVQ 80(SP), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(SP), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(SP), R13
	MOVQ 176(SP), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(DI)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(DI)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(DI)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(DI)

	// Result k
	MOVQ 8(SP), R10
	MOVQ 56(SP), R11
	MOVQ 104(SP), R12
	MOVQ 152(SP), R13
	MOVQ 160(SP), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(DI)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(DI)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(DI)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(DI)
	XORQ R10, R15

	// Result m
	MOVQ 40(SP), R11
	XORQ BX, R11
	MOVQ 88(SP), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(SP), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(SP), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(SP), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(DI)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(DI)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(DI)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(DI)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(DI)
	XORQ R11, R15

	// Result s
	MOVQ 16(SP), R10
	MOVQ 64(SP), R11
	MOVQ 112(SP), R12
	XORQ DX, R10
	MOVQ 120(SP), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(SP), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(DI)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(DI)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(DI)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(DI)
	MOVQ R8, 184(DI)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(DI), R12
	XORQ 56(DI), DX
	XORQ R15, BX
	XORQ 96(DI), R12
	XORQ 136(DI), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(DI), R13
	XORQ 64(DI), R8
	XORQ SI, CX
	XORQ 104(DI), R13
	XORQ 144(DI), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (DI), R10
	MOVQ 48(DI), R11
	XORQ R13, R9
	MOVQ 96(DI), R12
	MOVQ 144(DI), R13
	MOVQ 192(DI), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x8000000000008089, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (SP)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(SP)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(SP)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(SP)
	MOVQ R12, 8(SP)
	MOVQ R12, BP

	// Result g
	MOVQ 72(DI), R11
	XORQ R9, R11
	MOVQ 80(DI), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(DI), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(DI), R13
	MOVQ 176(DI), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(SP)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(SP)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(SP)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(SP)

	// Result k
	MOVQ 8(DI), R10
	MOVQ 56(DI), R11
	MOVQ 104(DI), R12
	MOVQ 152(DI), R13
	MOVQ 160(DI), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(SP)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(SP)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(SP)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(SP)
	XORQ R10, R15

	// Result m
	MOVQ 40(DI), R11
	XORQ BX, R11
	MOVQ 88(DI), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(DI), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(DI), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(DI), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(SP)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(SP)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(SP)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(SP)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(SP)
	XORQ R11, R15

	// Result s
	MOVQ 16(DI), R10
	MOVQ 64(DI), R11
	MOVQ 112(DI), R12
	XORQ DX, R10
	MOVQ 120(DI), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(DI), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(SP)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(SP)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(SP)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(SP)
	MOVQ R8, 184(SP)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(SP), R12
	XORQ 56(SP), DX
	XORQ R15, BX
	XORQ 96(SP), R12
	XORQ 136(SP), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(SP), R13
	XORQ 64(SP), R8
	XORQ SI, CX
	XORQ 104(SP), R13
	XORQ 144(SP), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (SP), R10
	MOVQ 48(SP), R11
	XORQ R13, R9
	MOVQ 96(SP), R12
	MOVQ 144(SP), R13
	MOVQ 192(SP), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x8000000000008003, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (DI)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(DI)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(DI)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(DI)
	MOVQ R12, 8(DI)
	MOVQ R12, BP

	// Result g
	MOVQ 72(SP), R11
	XORQ R9, R11
	MOVQ 80(SP), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(SP), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(SP), R13
	MOVQ 176(SP), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(DI)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(DI)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(DI)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(DI)

	// Result k
	MOVQ 8(SP), R10
	MOVQ 56(SP), R11
	MOVQ 104(SP), R12
	MOVQ 152(SP), R13
	MOVQ 160(SP), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(DI)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(DI)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(DI)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(DI)
	XORQ R10, R15

	// Result m
	MOVQ 40(SP), R11
	XORQ BX, R11
	MOVQ 88(SP), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(SP), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(SP), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(SP), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(DI)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(DI)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(DI)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(DI)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(DI)
	XORQ R11, R15

	// Result s
	MOVQ 16(SP), R10
	MOVQ 64(SP), R11
	MOVQ 112(SP), R12
	XORQ DX, R10
	MOVQ 120(SP), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(SP), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(DI)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(DI)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(DI)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(DI)
	MOVQ R8, 184(DI)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(DI), R12
	XORQ 56(DI), DX
	XORQ R15, BX
	XORQ 96(DI), R12
	XORQ 136(DI), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(DI), R13
	XORQ 64(DI), R8
	XORQ SI, CX
	XORQ 104(DI), R13
	XORQ 144(DI), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (DI), R10
	MOVQ 48(DI), R11
	XORQ R13, R9
	MOVQ 96(DI), R12
	MOVQ 144(DI), R13
	MOVQ 192(DI), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x8000000000008002, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (SP)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(SP)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(SP)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(SP)
	MOVQ R12, 8(SP)
	MOVQ R12, BP

	// Result g
	MOVQ 72(DI), R11
	XORQ R9, R11
	MOVQ 80(DI), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(DI), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(DI), R13
	MOVQ 176(DI), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(SP)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(SP)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(SP)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(SP)

	// Result k
	MOVQ 8(DI), R10
	MOVQ 56(DI), R11
	MOVQ 104(DI), R12
	MOVQ 152(DI), R13
	MOVQ 160(DI), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(SP)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(SP)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(SP)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(SP)
	XORQ R10, R15

	// Result m
	MOVQ 40(DI), R11
	XORQ BX, R11
	MOVQ 88(DI), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(DI), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(DI), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(DI), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(SP)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(SP)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(SP)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(SP)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(SP)
	XORQ R11, R15

	// Result s
	MOVQ 16(DI), R10
	MOVQ 64(DI), R11
	MOVQ 112(DI), R12
	XORQ DX, R10
	MOVQ 120(DI), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(DI), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(SP)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(SP)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(SP)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(SP)
	MOVQ R8, 184(SP)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(SP), R12
	XORQ 56(SP), DX
	XORQ R15, BX
	XORQ 96(SP), R12
	XORQ 136(SP), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(SP), R13
	XORQ 64(SP), R8
	XORQ SI, CX
	XORQ 104(SP), R13
	XORQ 144(SP), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (SP), R10
	MOVQ 48(SP), R11
	XORQ R13, R9
	MOVQ 96(SP), R12
	MOVQ 144(SP), R13
	MOVQ 192(SP), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x8000000000000080, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (DI)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(DI)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(DI)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(DI)
	MOVQ R12, 8(DI)
	MOVQ R12, BP

	// Result g
	MOVQ 72(SP), R11
	XORQ R9, R11
	MOVQ 80(SP), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(SP), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(SP), R13
	MOVQ 176(SP), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(DI)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(DI)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(DI)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(DI)

	// Result k
	MOVQ 8(SP), R10
	MOVQ 56(SP), R11
	MOVQ 104(SP), R12
	MOVQ 152(SP), R13
	MOVQ 160(SP), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(DI)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(DI)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(DI)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(DI)
	XORQ R10, R15

	// Result m
	MOVQ 40(SP), R11
	XORQ BX, R11
	MOVQ 88(SP), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(SP), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(SP), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(SP), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(DI)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(DI)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(DI)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(DI)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(DI)
	XORQ R11, R15

	// Result s
	MOVQ 16(SP), R10
	MOVQ 64(SP), R11
	MOVQ 112(SP), R12
	XORQ DX, R10
	MOVQ 120(SP), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(SP), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(DI)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(DI)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(DI)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(DI)
	MOVQ R8, 184(DI)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(DI), R12
	XORQ 56(DI), DX
	XORQ R15, BX
	XORQ 96(DI), R12
	XORQ 136(DI), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(DI), R13
	XORQ 64(DI), R8
	XORQ SI, CX
	XORQ 104(DI), R13
	XORQ 144(DI), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (DI), R10
	MOVQ 48(DI), R11
	XORQ R13, R9
	MOVQ 96(DI), R12
	MOVQ 144(DI), R13
	MOVQ 192(DI), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x000000000000800a, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (SP)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(SP)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(SP)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(SP)
	MOVQ R12, 8(SP)
	MOVQ R12, BP

	// Result g
	MOVQ 72(DI), R11
	XORQ R9, R11
	MOVQ 80(DI), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(DI), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(DI), R13
	MOVQ 176(DI), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(SP)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(SP)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(SP)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(SP)

	// Result k
	MOVQ 8(DI), R10
	MOVQ 56(DI), R11
	MOVQ 104(DI), R12
	MOVQ 152(DI), R13
	MOVQ 160(DI), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(SP)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(SP)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(SP)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(SP)
	XORQ R10, R15

	// Result m
	MOVQ 40(DI), R11
	XORQ BX, R11
	MOVQ 88(DI), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(DI), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(DI), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(DI), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(SP)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(SP)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(SP)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(SP)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(SP)
	XORQ R11, R15

	// Result s
	MOVQ 16(DI), R10
	MOVQ 64(DI), R11
	MOVQ 112(DI), R12
	XORQ DX, R10
	MOVQ 120(DI), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(DI), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(SP)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(SP)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(SP)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(SP)
	MOVQ R8, 184(SP)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(SP), R12
	XORQ 56(SP), DX
	XORQ R15, BX
	XORQ 96(SP), R12
	XORQ 136(SP), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(SP), R13
	XORQ 64(SP), R8
	XORQ SI, CX
	XORQ 104(SP), R13
	XORQ 144(SP), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (SP), R10
	MOVQ 48(SP), R11
	XORQ R13, R9
	MOVQ 96(SP), R12
	MOVQ 144(SP), R13
	MOVQ 192(SP), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x800000008000000a, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (DI)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(DI)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(DI)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(DI)
	MOVQ R12, 8(DI)
	MOVQ R12, BP

	// Result g
	MOVQ 72(SP), R11
	XORQ R9, R11
	MOVQ 80(SP), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(SP), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(SP), R13
	MOVQ 176(SP), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(DI)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(DI)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(DI)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(DI)

	// Result k
	MOVQ 8(SP), R10
	MOVQ 56(SP), R11
	MOVQ 104(SP), R12
	MOVQ 152(SP), R13
	MOVQ 160(SP), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(DI)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(DI)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(DI)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(DI)
	XORQ R10, R15

	// Result m
	MOVQ 40(SP), R11
	XORQ BX, R11
	MOVQ 88(SP), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(SP), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(SP), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(SP), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(DI)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(DI)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(DI)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(DI)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(DI)
	XORQ R11, R15

	// Result s
	MOVQ 16(SP), R10
	MOVQ 64(SP), R11
	MOVQ 112(SP), R12
	XORQ DX, R10
	MOVQ 120(SP), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(SP), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(DI)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(DI)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(DI)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(DI)
	MOVQ R8, 184(DI)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(DI), R12
	XORQ 56(DI), DX
	XORQ R15, BX
	XORQ 96(DI), R12
	XORQ 136(DI), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(DI), R13
	XORQ 64(DI), R8
	XORQ SI, CX
	XORQ 104(DI), R13
	XORQ 144(DI), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (DI), R10
	MOVQ 48(DI), R11
	XORQ R13, R9
	MOVQ 96(DI), R12
	MOVQ 144(DI), R13
	MOVQ 192(DI), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x8000000080008081, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (SP)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(SP)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(SP)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(SP)
	MOVQ R12, 8(SP)
	MOVQ R12, BP

	// Result g
	MOVQ 72(DI), R11
	XORQ R9, R11
	MOVQ 80(DI), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(DI), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(DI), R13
	MOVQ 176(DI), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(SP)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(SP)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(SP)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(SP)

	// Result k
	MOVQ 8(DI), R10
	MOVQ 56(DI), R11
	MOVQ 104(DI), R12
	MOVQ 152(DI), R13
	MOVQ 160(DI), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(SP)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(SP)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(SP)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(SP)
	XORQ R10, R15

	// Result m
	MOVQ 40(DI), R11
	XORQ BX, R11
	MOVQ 88(DI), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(DI), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(DI), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(DI), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(SP)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(SP)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(SP)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(SP)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(SP)
	XORQ R11, R15

	// Result s
	MOVQ 16(DI), R10
	MOVQ 64(DI), R11
	MOVQ 112(DI), R12
	XORQ DX, R10
	MOVQ 120(DI), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(DI), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(SP)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(SP)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(SP)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(SP)
	MOVQ R8, 184(SP)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(SP), R12
	XORQ 56(SP), DX
	XORQ R15, BX
	XORQ 96(SP), R12
	XORQ 136(SP), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(SP), R13
	XORQ 64(SP), R8
	XORQ SI, CX
	XORQ 104(SP), R13
	XORQ 144(SP), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (SP), R10
	MOVQ 48(SP), R11
	XORQ R13, R9
	MOVQ 96(SP), R12
	MOVQ 144(SP), R13
	MOVQ 192(SP), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x8000000000008080, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (DI)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(DI)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(DI)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(DI)
	MOVQ R12, 8(DI)
	MOVQ R12, BP

	// Result g
	MOVQ 72(SP), R11
	XORQ R9, R11
	MOVQ 80(SP), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(SP), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(SP), R13
	MOVQ 176(SP), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(DI)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(DI)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(DI)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(DI)

	// Result k
	MOVQ 8(SP), R10
	MOVQ 56(SP), R11
	MOVQ 104(SP), R12
	MOVQ 152(SP), R13
	MOVQ 160(SP), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(DI)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(DI)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(DI)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(DI)
	XORQ R10, R15

	// Result m
	MOVQ 40(SP), R11
	XORQ BX, R11
	MOVQ 88(SP), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(SP), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(SP), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(SP), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(DI)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(DI)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(DI)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(DI)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(DI)
	XORQ R11, R15

	// Result s
	MOVQ 16(SP), R10
	MOVQ 64(SP), R11
	MOVQ 112(SP), R12
	XORQ DX, R10
	MOVQ 120(SP), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(SP), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(DI)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(DI)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(DI)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(DI)
	MOVQ R8, 184(DI)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(DI), R12
	XORQ 56(DI), DX
	XORQ R15, BX
	XORQ 96(DI), R12
	XORQ 136(DI), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(DI), R13
	XORQ 64(DI), R8
	XORQ SI, CX
	XORQ 104(DI), R13
	XORQ 144(DI), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (DI), R10
	MOVQ 48(DI), R11
	XORQ R13, R9
	MOVQ 96(DI), R12
	MOVQ 144(DI), R13
	MOVQ 192(DI), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x0000000080000001, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (SP)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(SP)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(SP)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(SP)
	MOVQ R12, 8(SP)
	MOVQ R12, BP

	// Result g
	MOVQ 72(DI), R11
	XORQ R9, R11
	MOVQ 80(DI), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(DI), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(DI), R13
	MOVQ 176(DI), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(SP)
	XORQ AX, SI
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(SP)
	XORQ AX, BP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(SP)
	NOTQ R14
	XORQ R10, R15
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(SP)

	// Result k
	MOVQ 8(DI), R10
	MOVQ 56(DI), R11
	MOVQ 104(DI), R12
	MOVQ 152(DI), R13
	MOVQ 160(DI), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(SP)
	XORQ AX, SI
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(SP)
	XORQ AX, BP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(SP)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(SP)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(SP)
	XORQ R10, R15

	// Result m
	MOVQ 40(DI), R11
	XORQ BX, R11
	MOVQ 88(DI), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(DI), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(DI), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(DI), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(SP)
	XORQ AX, SI
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(SP)
	XORQ AX, BP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(SP)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(SP)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(SP)
	XORQ R11, R15

	// Result s
	MOVQ 16(DI), R10
	MOVQ 64(DI), R11
	MOVQ 112(DI), R12
	XORQ DX, R10
	MOVQ 120(DI), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(DI), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(SP)
	ROLQ $0x27, R12
	XORQ R9, R15
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(SP)
	XORQ BX, SI
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(SP)
	XORQ CX, BP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(SP)
	MOVQ R8, 184(SP)

	// Prepare round
	MOVQ BP, BX
	ROLQ $0x01, BX
	MOVQ 16(SP), R12
	XORQ 56(SP), DX
	XORQ R15, BX
	XORQ 96(SP), R12
	XORQ 136(SP), DX
	XORQ DX, R12
	MOVQ R12, CX
	ROLQ $0x01, CX
	MOVQ 24(SP), R13
	XORQ 64(SP), R8
	XORQ SI, CX
	XORQ 104(SP), R13
	XORQ 144(SP), R8
	XORQ R8, R13
	MOVQ R13, DX
	ROLQ $0x01, DX
	MOVQ R15, R8
	XORQ BP, DX
	ROLQ $0x01, R8
	MOVQ SI, R9
	XORQ R12, R8
	ROLQ $0x01, R9

	// Result b
	MOVQ (SP), R10
	MOVQ 48(SP), R11
	XORQ R13, R9
	MOVQ 96(SP), R12
	MOVQ 144(SP), R13
	MOVQ 192(SP), R14
	XORQ CX, R11
	ROLQ $0x2c, R11
	XORQ DX, R12
	XORQ BX, R10
	ROLQ $0x2b, R12
	MOVQ R11, SI
	MOVQ $0x8000000080008008, AX
	ORQ  R12, SI
	XORQ R10, AX
	XORQ AX, SI
	MOVQ SI, (DI)
	XORQ R9, R14
	ROLQ $0x0e, R14
	MOVQ R10, R15
	ANDQ R11, R15
	XORQ R14, R15
	MOVQ R15, 32(DI)
	XORQ R8, R13
	ROLQ $0x15, R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 16(DI)
	NOTQ R12
	ORQ  R10, R14
	ORQ  R13, R12
	XORQ R13, R14
	XORQ R11, R12
	MOVQ R14, 24(DI)
	MOVQ R12, 8(DI)
	NOP

	// Result g
	MOVQ 72(SP), R11
	XORQ R9, R11
	MOVQ 80(SP), R12
	ROLQ $0x14, R11
	XORQ BX, R12
	ROLQ $0x03, R12
	MOVQ 24(SP), R10
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ R8, R10
	MOVQ 128(SP), R13
	MOVQ 176(SP), R14
	ROLQ $0x1c, R10
	XORQ R10, AX
	MOVQ AX, 40(DI)
	NOP
	XORQ CX, R13
	ROLQ $0x2d, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 48(DI)
	NOP
	XORQ DX, R14
	ROLQ $0x3d, R14
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 64(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 72(DI)
	NOTQ R14
	NOP
	ORQ  R14, R13
	XORQ R12, R13
	MOVQ R13, 56(DI)

	// Result k
	MOVQ 8(SP), R10
	MOVQ 56(SP), R11
	MOVQ 104(SP), R12
	MOVQ 152(SP), R13
	MOVQ 160(SP), R14
	XORQ DX, R11
	ROLQ $0x06, R11
	XORQ R8, R12
	ROLQ $0x19, R12
	MOVQ R11, AX
	ORQ  R12, AX
	XORQ CX, R10
	ROLQ $0x01, R10
	XORQ R10, AX
	MOVQ AX, 80(DI)
	NOP
	XORQ R9, R13
	ROLQ $0x08, R13
	MOVQ R12, AX
	ANDQ R13, AX
	XORQ R11, AX
	MOVQ AX, 88(DI)
	NOP
	XORQ BX, R14
	ROLQ $0x12, R14
	NOTQ R13
	MOVQ R13, AX
	ANDQ R14, AX
	XORQ R12, AX
	MOVQ AX, 96(DI)
	MOVQ R14, AX
	ORQ  R10, AX
	XORQ R13, AX
	MOVQ AX, 104(DI)
	ANDQ R11, R10
	XORQ R14, R10
	MOVQ R10, 112(DI)
	NOP

	// Result m
	MOVQ 40(SP), R11
	XORQ BX, R11
	MOVQ 88(SP), R12
	ROLQ $0x24, R11
	XORQ CX, R12
	MOVQ 32(SP), R10
	ROLQ $0x0a, R12
	MOVQ R11, AX
	MOVQ 136(SP), R13
	ANDQ R12, AX
	XORQ R9, R10
	MOVQ 184(SP), R14
	ROLQ $0x1b, R10
	XORQ R10, AX
	MOVQ AX, 120(DI)
	NOP
	XORQ DX, R13
	ROLQ $0x0f, R13
	MOVQ R12, AX
	ORQ  R13, AX
	XORQ R11, AX
	MOVQ AX, 128(DI)
	NOP
	XORQ R8, R14
	ROLQ $0x38, R14
	NOTQ R13
	MOVQ R13, AX
	ORQ  R14, AX
	XORQ R12, AX
	MOVQ AX, 136(DI)
	ORQ  R10, R11
	XORQ R14, R11
	MOVQ R11, 152(DI)
	ANDQ R10, R14
	XORQ R13, R14
	MOVQ R14, 144(DI)
	NOP

	// Result s
	MOVQ 16(SP), R10
	MOVQ 64(SP), R11
	MOVQ 112(SP), R12
	XORQ DX, R10
	MOVQ 120(SP), R13
	ROLQ $0x3e, R10
	XORQ R8, R11
	MOVQ 168(SP), R14
	ROLQ $0x37, R11
	XORQ R9, R12
	MOVQ R10, R9
	XORQ CX, R14
	ROLQ $0x02, R14
	ANDQ R11, R9
	XORQ R14, R9
	MOVQ R9, 192(DI)
	ROLQ $0x27, R12
	NOP
	NOTQ R11
	XORQ BX, R13
	MOVQ R11, BX
	ANDQ R12, BX
	XORQ R10, BX
	MOVQ BX, 160(DI)
	NOP
	ROLQ $0x29, R13
	MOVQ R12, CX
	ORQ  R13, CX
	XORQ R11, CX
	MOVQ CX, 168(DI)
	NOP
	MOVQ R13, DX
	MOVQ R14, R8
	ANDQ R14, DX
	ORQ  R10, R8
	XORQ R12, DX
	XORQ R13, R8
	MOVQ DX, 176(DI)
	MOVQ R8, 184(DI)

	// Revert the internal state to the user state
	NOTQ 8(DI)
	NOTQ 16(DI)
	NOTQ 64(DI)
	NOTQ 96(DI)
	NOTQ 136(DI)
	NOTQ 160(DI)
	RET
