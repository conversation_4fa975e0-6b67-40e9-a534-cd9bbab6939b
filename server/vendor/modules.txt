# filippo.io/edwards25519 v1.1.0
## explicit; go 1.20
filippo.io/edwards25519
filippo.io/edwards25519/field
# github.com/ArtisanCloud/PowerLibs/v3 v3.0.13
## explicit; go 1.17
github.com/ArtisanCloud/PowerLibs/v3/cache
github.com/ArtisanCloud/PowerLibs/v3/fmt
github.com/ArtisanCloud/PowerLibs/v3/http/contract
github.com/ArtisanCloud/PowerLibs/v3/http/dataflow
github.com/ArtisanCloud/PowerLibs/v3/http/drivers/http
github.com/ArtisanCloud/PowerLibs/v3/http/helper
github.com/ArtisanCloud/PowerLibs/v3/logger
github.com/ArtisanCloud/PowerLibs/v3/logger/contract
github.com/ArtisanCloud/PowerLibs/v3/logger/drivers/zap
github.com/ArtisanCloud/PowerLibs/v3/object
github.com/ArtisanCloud/PowerLibs/v3/os
# github.com/ArtisanCloud/PowerWeChat/v3 v3.1.1
## explicit; go 1.17
github.com/ArtisanCloud/PowerWeChat/v3/src/basicService/subscribeMessage
github.com/ArtisanCloud/PowerWeChat/v3/src/basicService/subscribeMessage/request
github.com/ArtisanCloud/PowerWeChat/v3/src/basicService/subscribeMessage/response
github.com/ArtisanCloud/PowerWeChat/v3/src/kernel
github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/contract
github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/decorators
github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/messages
github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/models
github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/power
github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/providers
github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/request
github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/response
github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/support
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/auth
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/auth/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/base
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/base/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/base/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/customerServiceMessage
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/customerServiceMessage/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/customerServiceMessage/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/dataCube
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/dataCube/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/dataCube/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/express
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/express/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/image
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/image/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/immediateDelivery
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/immediateDelivery/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/industry/miniDrama/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/industry/miniDrama/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/industry/miniDrama/vod
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/internet
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/liveBroadcast
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/liveBroadcast/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/liveBroadcast/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/nearbyPoi
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/nearbyPoi/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/ocr
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/ocr/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/operation
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/operation/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/phoneNumber
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/phoneNumber/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/plugin
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/plugin/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/riskControl
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/riskControl/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/riskControl/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/search
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/search/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/security
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/security/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/server
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/serviceMarket
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/serviceMarket/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/serviceMarket/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/shortLink
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/shortLink/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/soter
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/soter/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/soter/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/uniformMessage
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/uniformMessage/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/updatableMessage
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/updatableMessage/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/updatableMessage/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/urlLink
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/urlLink/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/urlLink/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/urlScheme
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/urlScheme/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/urlScheme/response
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/virtualPayment
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/virtualPayment/request
github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/wxaCode
github.com/ArtisanCloud/PowerWeChat/v3/src/officialAccount/device/response
github.com/ArtisanCloud/PowerWeChat/v3/src/officialAccount/server
github.com/ArtisanCloud/PowerWeChat/v3/src/work/media/response
github.com/ArtisanCloud/PowerWeChat/v3/src/work/server/handlers
# github.com/bytedance/sonic v1.14.0
## explicit; go 1.17
github.com/bytedance/sonic
github.com/bytedance/sonic/ast
github.com/bytedance/sonic/decoder
github.com/bytedance/sonic/encoder
github.com/bytedance/sonic/internal/caching
github.com/bytedance/sonic/internal/compat
github.com/bytedance/sonic/internal/cpu
github.com/bytedance/sonic/internal/decoder/api
github.com/bytedance/sonic/internal/decoder/consts
github.com/bytedance/sonic/internal/decoder/errors
github.com/bytedance/sonic/internal/decoder/jitdec
github.com/bytedance/sonic/internal/decoder/optdec
github.com/bytedance/sonic/internal/encoder
github.com/bytedance/sonic/internal/encoder/alg
github.com/bytedance/sonic/internal/encoder/ir
github.com/bytedance/sonic/internal/encoder/vars
github.com/bytedance/sonic/internal/encoder/vm
github.com/bytedance/sonic/internal/encoder/x86
github.com/bytedance/sonic/internal/envs
github.com/bytedance/sonic/internal/jit
github.com/bytedance/sonic/internal/native
github.com/bytedance/sonic/internal/native/avx2
github.com/bytedance/sonic/internal/native/neon
github.com/bytedance/sonic/internal/native/sse
github.com/bytedance/sonic/internal/native/types
github.com/bytedance/sonic/internal/optcaching
github.com/bytedance/sonic/internal/resolver
github.com/bytedance/sonic/internal/rt
github.com/bytedance/sonic/internal/utils
github.com/bytedance/sonic/option
github.com/bytedance/sonic/unquote
github.com/bytedance/sonic/utf8
# github.com/bytedance/sonic/loader v0.3.0
## explicit; go 1.16
github.com/bytedance/sonic/loader
github.com/bytedance/sonic/loader/internal/abi
github.com/bytedance/sonic/loader/internal/iasm/expr
github.com/bytedance/sonic/loader/internal/iasm/x86_64
github.com/bytedance/sonic/loader/internal/rt
# github.com/cespare/xxhash/v2 v2.2.0
## explicit; go 1.11
github.com/cespare/xxhash/v2
# github.com/cloudwego/base64x v0.1.5
## explicit; go 1.16
github.com/cloudwego/base64x
github.com/cloudwego/base64x/internal/native
github.com/cloudwego/base64x/internal/native/avx2
github.com/cloudwego/base64x/internal/native/sse
github.com/cloudwego/base64x/internal/rt
# github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f
## explicit
github.com/dgryski/go-rendezvous
# github.com/gabriel-vasile/mimetype v1.4.9
## explicit; go 1.23.0
github.com/gabriel-vasile/mimetype
github.com/gabriel-vasile/mimetype/internal/charset
github.com/gabriel-vasile/mimetype/internal/json
github.com/gabriel-vasile/mimetype/internal/magic
# github.com/gin-contrib/sse v1.1.0
## explicit; go 1.23
github.com/gin-contrib/sse
# github.com/gin-gonic/gin v1.10.1
## explicit; go 1.20
github.com/gin-gonic/gin
github.com/gin-gonic/gin/binding
github.com/gin-gonic/gin/internal/bytesconv
github.com/gin-gonic/gin/internal/json
github.com/gin-gonic/gin/render
# github.com/go-playground/locales v0.14.1
## explicit; go 1.17
github.com/go-playground/locales
github.com/go-playground/locales/currency
# github.com/go-playground/universal-translator v0.18.1
## explicit; go 1.18
github.com/go-playground/universal-translator
# github.com/go-playground/validator/v10 v10.27.0
## explicit; go 1.20
github.com/go-playground/validator/v10
# github.com/go-sql-driver/mysql v1.9.3
## explicit; go 1.21.0
github.com/go-sql-driver/mysql
# github.com/goccy/go-json v0.10.5
## explicit; go 1.19
github.com/goccy/go-json
github.com/goccy/go-json/internal/decoder
github.com/goccy/go-json/internal/encoder
github.com/goccy/go-json/internal/encoder/vm
github.com/goccy/go-json/internal/encoder/vm_color
github.com/goccy/go-json/internal/encoder/vm_color_indent
github.com/goccy/go-json/internal/encoder/vm_indent
github.com/goccy/go-json/internal/errors
github.com/goccy/go-json/internal/runtime
# github.com/golang-jwt/jwt/v5 v5.2.3
## explicit; go 1.18
github.com/golang-jwt/jwt/v5
# github.com/jinzhu/inflection v1.0.0
## explicit
github.com/jinzhu/inflection
# github.com/jinzhu/now v1.1.5
## explicit; go 1.12
github.com/jinzhu/now
# github.com/joho/godotenv v1.5.1
## explicit; go 1.12
github.com/joho/godotenv
# github.com/json-iterator/go v1.1.12
## explicit; go 1.12
github.com/json-iterator/go
# github.com/klauspost/cpuid/v2 v2.3.0
## explicit; go 1.22
github.com/klauspost/cpuid/v2
# github.com/leodido/go-urn v1.4.0
## explicit; go 1.18
github.com/leodido/go-urn
github.com/leodido/go-urn/scim/schema
# github.com/mattn/go-isatty v0.0.20
## explicit; go 1.15
github.com/mattn/go-isatty
# github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
## explicit
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v1.0.2
## explicit; go 1.12
github.com/modern-go/reflect2
# github.com/patrickmn/go-cache v2.1.0+incompatible
## explicit
github.com/patrickmn/go-cache
# github.com/pelletier/go-toml/v2 v2.2.4
## explicit; go 1.21.0
github.com/pelletier/go-toml/v2
github.com/pelletier/go-toml/v2/internal/characters
github.com/pelletier/go-toml/v2/internal/danger
github.com/pelletier/go-toml/v2/internal/tracker
github.com/pelletier/go-toml/v2/unstable
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/redis/go-redis/v9 v9.0.3
## explicit; go 1.18
github.com/redis/go-redis/v9
github.com/redis/go-redis/v9/internal
github.com/redis/go-redis/v9/internal/hashtag
github.com/redis/go-redis/v9/internal/hscan
github.com/redis/go-redis/v9/internal/pool
github.com/redis/go-redis/v9/internal/proto
github.com/redis/go-redis/v9/internal/rand
github.com/redis/go-redis/v9/internal/util
# github.com/twitchyliquid64/golang-asm v0.15.1
## explicit; go 1.13
github.com/twitchyliquid64/golang-asm/asm/arch
github.com/twitchyliquid64/golang-asm/bio
github.com/twitchyliquid64/golang-asm/dwarf
github.com/twitchyliquid64/golang-asm/goobj
github.com/twitchyliquid64/golang-asm/obj
github.com/twitchyliquid64/golang-asm/obj/arm
github.com/twitchyliquid64/golang-asm/obj/arm64
github.com/twitchyliquid64/golang-asm/obj/mips
github.com/twitchyliquid64/golang-asm/obj/ppc64
github.com/twitchyliquid64/golang-asm/obj/riscv
github.com/twitchyliquid64/golang-asm/obj/s390x
github.com/twitchyliquid64/golang-asm/obj/wasm
github.com/twitchyliquid64/golang-asm/obj/x86
github.com/twitchyliquid64/golang-asm/objabi
github.com/twitchyliquid64/golang-asm/src
github.com/twitchyliquid64/golang-asm/sys
github.com/twitchyliquid64/golang-asm/unsafeheader
# github.com/ugorji/go/codec v1.3.0
## explicit; go 1.21
github.com/ugorji/go/codec
# go.uber.org/atomic v1.7.0
## explicit; go 1.13
go.uber.org/atomic
# go.uber.org/multierr v1.6.0
## explicit; go 1.12
go.uber.org/multierr
# go.uber.org/zap v1.21.0
## explicit; go 1.13
go.uber.org/zap
go.uber.org/zap/buffer
go.uber.org/zap/internal/bufferpool
go.uber.org/zap/internal/color
go.uber.org/zap/internal/exit
go.uber.org/zap/zapcore
# golang.org/x/arch v0.19.0
## explicit; go 1.23.0
golang.org/x/arch/x86/x86asm
# golang.org/x/crypto v0.40.0
## explicit; go 1.23.0
golang.org/x/crypto/sha3
# golang.org/x/net v0.42.0
## explicit; go 1.23.0
golang.org/x/net/html
golang.org/x/net/html/atom
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/h2c
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/httpcommon
# golang.org/x/sys v0.34.0
## explicit; go 1.23.0
golang.org/x/sys/cpu
golang.org/x/sys/unix
# golang.org/x/text v0.27.0
## explicit; go 1.23.0
golang.org/x/text/cases
golang.org/x/text/internal
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/tag
golang.org/x/text/language
golang.org/x/text/secure/bidirule
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
# google.golang.org/protobuf v1.36.6
## explicit; go 1.22
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/proto
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# gorm.io/driver/mysql v1.6.0
## explicit; go 1.18
gorm.io/driver/mysql
# gorm.io/gorm v1.30.1
## explicit; go 1.18
gorm.io/gorm
gorm.io/gorm/callbacks
gorm.io/gorm/clause
gorm.io/gorm/internal/lru
gorm.io/gorm/internal/stmt_store
gorm.io/gorm/logger
gorm.io/gorm/migrator
gorm.io/gorm/schema
gorm.io/gorm/utils
