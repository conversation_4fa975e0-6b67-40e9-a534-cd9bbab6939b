package models

import (
	"time"

	"gorm.io/gorm"
)

// --- Database Models ---

// User 用户表 (使用现有NocoBase结构)
type User struct {
	ID               uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt        *time.Time `json:"created_at"`
	UpdatedAt        *time.Time `json:"updated_at"`
	Nickname         *string    `json:"nickname"`
	Username         *string    `gorm:"uniqueIndex" json:"username"`
	Email            *string    `gorm:"uniqueIndex" json:"email"`
	Phone            *string    `gorm:"uniqueIndex" json:"phone"`
	Password         *string    `json:"-"`
	PasswordChangeTz *int64     `json:"password_change_tz"`
	AppLang          *string    `json:"app_lang"`
	ResetToken       *string    `gorm:"uniqueIndex" json:"reset_token"`
	SystemSettings   *string    `gorm:"type:json" json:"system_settings"`
	Sort             *int64     `json:"sort"`
	CreatedByID      *uint64    `json:"created_by_id"`
	UpdatedByID      *uint64    `json:"updated_by_id"`

	// 微信相关字段
	WechatOpenID  *string `gorm:"uniqueIndex" json:"wechat_openid"`  // 微信小程序openid
	WechatUnionID *string `gorm:"uniqueIndex" json:"wechat_unionid"` // 微信unionid
	Avatar        *string `json:"avatar"`                            // 用户头像URL
	SessionKey    *string `json:"-"`                                 // 微信session_key，不返回给前端
}

// Duanju 短剧表 (新表结构)
type Duanju struct {
	ID                     uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt              *time.Time `json:"created_at"`
	UpdatedAt              *time.Time `json:"updated_at"`
	CreatedByID            *uint64    `json:"created_by_id"`
	UpdatedByID            *uint64    `json:"updated_by_id"`
	BookName               *string    `json:"book_name"`                       // 短剧名称
	BookID                 *string    `json:"book_id"`                         // 外部书籍ID
	SerialCount            *float64   `json:"serial_count"`                    // 连载数量
	ReadCount              *uint64    `json:"read_count"`                      // 阅读数量
	ReadCountLastUpdatedTs *uint64    `json:"read_count_last_updated_ts"`      // 阅读数量最后更新时间戳
	Abstract               *string    `gorm:"type:text" json:"abstract"`       // 摘要
	Platform               *string    `json:"platform"`                        // 平台信息
	Recommendation         *string    `gorm:"type:text" json:"recommendation"` // 推荐语
	IsPublic               *int8      `gorm:"default:1" json:"is_public"`      // 是否公开
	HeatCount              *uint64    `json:"heat_count"`                      // 热度数量

	// Associations
	Actors     []DuanjuActor     `gorm:"many2many:t_o4wwn0qrn4t;foreignKey:ID;joinForeignKey:f_r9xdgapk60j;References:ID;joinReferences:f_q8e962zcn20" json:"actors,omitempty"`
	Categories []DuanjuCategory  `gorm:"many2many:t_ofejlo6jzf9;foreignKey:ID;joinForeignKey:f_e6dqsc9q8j7;References:ID;joinReferences:f_xeblmd9tsf8" json:"categories,omitempty"`
	Authors    []DuanjuAuthor    `gorm:"many2many:t_mjtaw8zxvjl;foreignKey:ID;joinForeignKey:f_8nltnm6gzxj;References:ID;joinReferences:f_hlfdisq43sx" json:"authors,omitempty"`
	Episodes   []DuanjuEpisode   `gorm:"foreignKey:DuanjuID" json:"episodes,omitempty"`
	SeriesCuts []DuanjuSeriesCut `gorm:"foreignKey:DuanjuID" json:"series_cuts,omitempty"`
	Comments   []DuanjuComment   `gorm:"foreignKey:FL77p9koj8sc" json:"comments,omitempty"`
}

func (Duanju) TableName() string {
	return "duanju"
}

// DuanjuActor 短剧演员表 (新表结构)
type DuanjuActor struct {
	ID               uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt        *time.Time `json:"created_at"`
	UpdatedAt        *time.Time `json:"updated_at"`
	CreatedByID      *uint64    `json:"created_by_id"`
	UpdatedByID      *uint64    `json:"updated_by_id"`
	Name             *string    `json:"name"`                  // 演员姓名
	Platform         *string    `json:"platform"`              // 平台信息
	AvatarURL        *string    `json:"avatar_url"`            // 头像URL
	XhsID            *string    `json:"xhs_id"`                // 小红书ID
	WeiboID          *string    `json:"weibo_id"`              // 微博ID
	WeboSuperIndexID *string    `json:"webo_super_index_id"`   // 微博超话指数ID
	ActInCounts      *uint64    `json:"act_in_counts"`         // 参演数量
	Desc             *string    `gorm:"type:text" json:"desc"` // 描述
	DouyinID         *string    `json:"douyin_id"`             // 抖音ID
	Gender           *string    `json:"gender"`                // 性别

	// Associations
	Duanjus   []Duanju             `gorm:"many2many:t_o4wwn0qrn4t;foreignKey:ID;joinForeignKey:f_q8e962zcn20;References:ID;joinReferences:f_r9xdgapk60j" json:"duanjus,omitempty"`
	ActorCuts []DuanjuActorCut     `gorm:"foreignKey:ActorID" json:"actor_cuts,omitempty"`
	Comments  []DuanjuActorComment `gorm:"foreignKey:FBd9p0idzl6i" json:"comments,omitempty"`
	Followers []User               `gorm:"many2many:t_q95zevchrgq;foreignKey:ID;joinForeignKey:f_6edhr0wcmio;References:ID;joinReferences:f_wjxm1ueyqdw" json:"followers,omitempty"`
}

func (DuanjuActor) TableName() string {
	return "duanju_actors"
}

// DuanjuCategory 短剧分类表 (新表结构)
type DuanjuCategory struct {
	ID           uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt    *time.Time `json:"created_at"`
	UpdatedAt    *time.Time `json:"updated_at"`
	CreatedByID  *uint64    `json:"created_by_id"`
	UpdatedByID  *uint64    `json:"updated_by_id"`
	Name         *string    `json:"name"`                  // 分类名称
	Platform     *string    `json:"platform"`              // 平台信息
	CatID        *string    `json:"cat_id"`                // 分类ID
	F4vpgcuhqxjl *uint64    `json:"f_4vpgcuhqxjl"`         // 关联字段
	HeatCount    *uint64    `json:"heat_count"`            // 热度数量
	Desc         *string    `gorm:"type:text" json:"desc"` // 描述

	// Associations
	Duanjus []Duanju `gorm:"many2many:t_ofejlo6jzf9;foreignKey:ID;joinForeignKey:f_xeblmd9tsf8;References:ID;joinReferences:f_e6dqsc9q8j7" json:"duanjus,omitempty"`
}

func (DuanjuCategory) TableName() string {
	return "duanju_categories"
}

// DuanjuAuthor 短剧制作方表 (新表结构)
type DuanjuAuthor struct {
	ID          uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
	CreatedByID *uint64    `json:"created_by_id"`
	UpdatedByID *uint64    `json:"updated_by_id"`
	Name        *string    `json:"name"`                  // 制作方名称
	Platform    *string    `json:"platform"`              // 平台信息
	Desc        *string    `gorm:"type:text" json:"desc"` // 描述

	// Associations
	Duanjus []Duanju `gorm:"many2many:t_mjtaw8zxvjl;foreignKey:ID;joinForeignKey:f_hlfdisq43sx;References:ID;joinReferences:f_8nltnm6gzxj" json:"duanjus,omitempty"`
}

func (DuanjuAuthor) TableName() string {
	return "duanju_authors"
}

// DuanjuActorCut 演员精彩片段表 (新表结构)
type DuanjuActorCut struct {
	ID          uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
	CreatedByID *uint64    `json:"created_by_id"`
	UpdatedByID *uint64    `json:"updated_by_id"`
	Title       *string    `json:"title"`                 // 标题
	Desc        *string    `gorm:"type:text" json:"desc"` // 描述
	PlayCount   *uint64    `json:"play_count"`            // 播放数量
	Duration    *uint64    `json:"duration"`              // 时长
	ActorID     *uint64    `json:"actor_id"`              // 演员ID

	// Associations
	Actor *DuanjuActor `gorm:"foreignKey:ActorID" json:"actor,omitempty"`
}

func (DuanjuActorCut) TableName() string {
	return "duanju_actor_cuts"
}

// DuanjuSeriesCut 短剧剧集精彩片段表 (新表结构)
type DuanjuSeriesCut struct {
	ID          uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
	CreatedByID *uint64    `json:"created_by_id"`
	UpdatedByID *uint64    `json:"updated_by_id"`
	Title       *string    `json:"title"`                 // 标题
	Desc        *string    `gorm:"type:text" json:"desc"` // 描述
	Duration    *uint64    `json:"duration"`              // 时长
	PlayCount   *uint64    `json:"play_count"`            // 播放数量
	DuanjuID    *uint64    `json:"duanju_id"`             // 短剧ID

	// Associations
	Duanju *Duanju `gorm:"foreignKey:DuanjuID" json:"duanju,omitempty"`
}

func (DuanjuSeriesCut) TableName() string {
	return "duanju_series_cuts"
}

// DuanjuEpisode 短剧剧集表 (从collections中推断的结构)
type DuanjuEpisode struct {
	ID          uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
	CreatedByID *uint64    `json:"created_by_id"`
	UpdatedByID *uint64    `json:"updated_by_id"`
	Title       *string    `json:"title"`       // 剧集标题
	DuanjuID    *uint64    `json:"duanju_id"`   // 短剧ID
	EpisodeNum  *int       `json:"episode_num"` // 集数
	Duration    *uint64    `json:"duration"`    // 时长
	VideoURL    *string    `json:"video_url"`   // 视频URL
	CoverURL    *string    `json:"cover_url"`   // 封面URL

	// Associations
	Duanju *Duanju `gorm:"foreignKey:DuanjuID" json:"duanju,omitempty"`
}

func (DuanjuEpisode) TableName() string {
	return "duanju_episodes"
}

// DuanjuNews 新闻表 (使用duanju_news表结构)
type DuanjuNews struct {
	ID          uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
	CreatedByID *uint64    `json:"created_by_id"`
	UpdatedByID *uint64    `json:"updated_by_id"`
	Title       *string    `gorm:"size:255" json:"title"`
	Description *string    `gorm:"type:text" json:"description"`
	Content     *string    `gorm:"type:longtext" json:"content"`
	Source      *string    `gorm:"size:255" json:"source"`
	Cover       *string    `gorm:"size:500" json:"cover"`      // 新增封面字段
	PublishTime *string    `gorm:"size:50" json:"publishTime"` // 新增发布时间字段
	DuanjuID    *uint64    `json:"duanju_id"`                  // 关联短剧ID
	ActorID     *uint64    `json:"actor_id"`                   // 关联演员ID
	Status      int8       `gorm:"default:1;comment:1:正常 0:下架" json:"status"`

	// Associations
	Duanju *Duanju      `gorm:"foreignKey:DuanjuID" json:"duanju,omitempty"`
	Actor  *DuanjuActor `gorm:"foreignKey:ActorID" json:"actor,omitempty"`
}

func (DuanjuNews) TableName() string {
	return "duanju_news"
}

// DuanjuComment 短剧评论表 (使用duanju_comments表结构)
type DuanjuComment struct {
	ID           uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt    *time.Time `json:"created_at"`
	UpdatedAt    *time.Time `json:"updated_at"`
	CreatedByID  *uint64    `json:"created_by_id"` // 评论创建者ID (对应User.ID)
	UpdatedByID  *uint64    `json:"updated_by_id"` // 评论更新者ID
	Content      *string    `gorm:"type:text" json:"content"`
	Status       int8       `gorm:"default:1;comment:1:正常 0:删除" json:"status"`
	UserID       *uint64    `json:"user_id"`                                   // 评论用户ID (冗余字段，与CreatedByID相同)
	FL77p9koj8sc *uint64    `gorm:"column:f_l77p9koj8sc" json:"f_l77p9koj8sc"` // NocoBase关联字段，关联短剧ID

	// Associations
	User   *User   `gorm:"foreignKey:CreatedByID" json:"user,omitempty"`
	Duanju *Duanju `gorm:"foreignKey:FL77p9koj8sc" json:"duanju,omitempty"`
}

func (DuanjuComment) TableName() string {
	return "duanju_comments"
}

// DuanjuActorComment 演员评论表 (使用duanju_actor_comments表结构)
type DuanjuActorComment struct {
	ID           uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt    *time.Time `json:"created_at"`
	UpdatedAt    *time.Time `json:"updated_at"`
	CreatedByID  *uint64    `json:"created_by_id"` // 评论创建者ID (对应User.ID)
	UpdatedByID  *uint64    `json:"updated_by_id"` // 评论更新者ID
	Content      *string    `gorm:"type:text" json:"content"`
	Status       int8       `gorm:"default:1;comment:1:正常 0:删除" json:"status"`
	UserID       *uint64    `json:"user_id"`                                   // 评论用户ID (冗余字段，与CreatedByID相同)
	FBd9p0idzl6i *uint64    `gorm:"column:f_bd9p0idzl6i" json:"f_bd9p0idzl6i"` // NocoBase关联字段，关联演员ID

	// Associations
	User  *User        `gorm:"foreignKey:CreatedByID" json:"user,omitempty"`
	Actor *DuanjuActor `gorm:"foreignKey:FBd9p0idzl6i" json:"actor,omitempty"`
}

func (DuanjuActorComment) TableName() string {
	return "duanju_actor_comments"
}

// Attachment 附件表 (nocobase生成的表结构)
type Attachment struct {
	ID          uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt   time.Time `gorm:"not null" json:"created_at"`
	UpdatedAt   time.Time `gorm:"not null" json:"updated_at"`
	Title       *string   `gorm:"size:255;comment:用户文件名（不含扩展名）" json:"title"`
	Filename    *string   `gorm:"size:255;comment:系统文件名（含扩展名）" json:"filename"`
	Extname     *string   `gorm:"size:255;comment:扩展名（含\".\"）" json:"extname"`
	Size        *int      `gorm:"comment:文件体积（字节）" json:"size"`
	Mimetype    *string   `gorm:"size:255" json:"mimetype"`
	Path        *string   `gorm:"type:text;comment:相对路径（含\"/\"前缀）" json:"path"`
	Meta        *string   `gorm:"type:json;comment:其他文件信息（如图片的宽高）" json:"meta"`
	URL         *string   `gorm:"type:text;comment:网络访问地址" json:"url"`
	CreatedByID *uint64   `json:"created_by_id"`
	UpdatedByID *uint64   `json:"updated_by_id"`
	StorageID   *uint64   `json:"storage_id"`
}

func (Attachment) TableName() string {
	return "attachments"
}

// Field nocobase字段配置表
type Field struct {
	Key            string     `gorm:"primaryKey;size:255" json:"key"`
	Name           string     `gorm:"size:255" json:"name"`
	Type           string     `gorm:"size:255" json:"type"`
	Interface      string     `gorm:"size:255" json:"interface"`
	CollectionName string     `gorm:"size:255" json:"collection_name"`
	ParentKey      *string    `gorm:"size:255" json:"parent_key"`
	ReverseKey     *string    `gorm:"size:255" json:"reverse_key"`
	Options        string     `gorm:"type:json" json:"options"`
	Sort           *int       `json:"sort"`
	CreatedAt      *time.Time `json:"created_at"`
	UpdatedAt      *time.Time `json:"updated_at"`
}

func (Field) TableName() string {
	return "fields"
}

// UserFollow 用户关注表 (已废弃，关注关系现在通过中间表t_q95zevchrgq处理)
// 保留此结构用于兼容性，但实际关联关系在User和DuanjuActor中定义

type PaymentOrder struct {
	ID            string         `gorm:"primaryKey;size:50" json:"id"`
	UserID        uint64         `gorm:"not null" json:"user_id"`
	ContentType   string         `gorm:"size:20;not null" json:"content_type"` // drama, cut
	ContentID     string         `gorm:"size:50;not null" json:"content_id"`
	PaymentMethod string         `gorm:"size:20;not null" json:"payment_method"` // wechat, alipay, apple_pay
	Amount        float64        `gorm:"type:decimal(10,2);not null" json:"amount"`
	Status        string         `gorm:"size:20;default:pending" json:"status"` // pending, paid, failed, refunded
	UnlockedAt    *time.Time     `json:"unlocked_at"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"-"`

	// Associations
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// DailyFortune 今日运势表 (新表结构)
type DailyFortune struct {
	ID          uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt   *time.Time `json:"created_at"`
	UpdatedAt   *time.Time `json:"updated_at"`
	CreatedByID *uint64    `json:"created_by_id"`
	UpdatedByID *uint64    `json:"updated_by_id"`
	Today       *time.Time `gorm:"type:date" json:"today"` // 日期
	GoodText    *string    `json:"good_text"`              // 好运文本
	BadText     *string    `json:"bad_text"`               // 坏运文本
}

func (DailyFortune) TableName() string {
	return "daily_fortune"
}

// 微信登录相关结构

// WechatLoginRequest 微信登录请求
type WechatLoginRequest struct {
	Code string `json:"code" binding:"required"` // 微信登录code
}

// WechatLoginResponse 微信登录响应
type WechatLoginResponse struct {
	Token string `json:"token"` // JWT token
	User  User   `json:"user"`  // 用户信息
}

// WechatPhoneRequest 微信手机号解密请求
type WechatPhoneRequest struct {
	EncryptedData string `json:"encryptedData" binding:"required"` // 加密数据
	IV            string `json:"iv" binding:"required"`            // 初始向量
	SessionKey    string `json:"sessionKey"`                       // session_key（可选，从用户信息中获取）
}

// WechatPhoneResponse 微信手机号解密响应
type WechatPhoneResponse struct {
	PhoneNumber     string `json:"phoneNumber"`     // 手机号
	PurePhoneNumber string `json:"purePhoneNumber"` // 纯手机号（无区号）
	CountryCode     string `json:"countryCode"`     // 国家代码
}

// UserProfileUpdateRequest 用户信息更新请求
type UserProfileUpdateRequest struct {
	Nickname *string `json:"nickname"` // 昵称
	Avatar   *string `json:"avatar"`   // 头像URL
}

// 微信API响应结构

// WechatCode2SessionResponse 微信code2session响应
type WechatCode2SessionResponse struct {
	OpenID     string `json:"openid"`      // 用户唯一标识
	SessionKey string `json:"session_key"` // 会话密钥
	UnionID    string `json:"unionid"`     // 用户在开放平台的唯一标识符
	ErrCode    int    `json:"errcode"`     // 错误码
	ErrMsg     string `json:"errmsg"`      // 错误信息
}

// WechatPhoneInfo 微信手机号信息
type WechatPhoneInfo struct {
	PhoneNumber     string `json:"phoneNumber"`     // 用户绑定的手机号（国外手机号会有区号）
	PurePhoneNumber string `json:"purePhoneNumber"` // 没有区号的手机号
	CountryCode     string `json:"countryCode"`     // 区号
	Watermark       struct {
		Timestamp int64  `json:"timestamp"` // 时间戳
		AppID     string `json:"appid"`     // 小程序appid
	} `json:"watermark"` // 数据水印
}

type RankingConfig struct {
	ID          string         `gorm:"primaryKey;size:50" json:"id"`
	Type        string         `gorm:"size:50;not null" json:"type"` // hot, weird, actor_total, actor_male, actor_female
	Name        string         `gorm:"size:100;not null" json:"name"`
	Description *string        `gorm:"type:text" json:"description"`
	Status      int8           `gorm:"default:1;comment:1:启用 0:禁用" json:"status"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

type AppConfig struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	ConfigKey   string         `gorm:"size:100;not null;uniqueIndex" json:"config_key"`
	ConfigValue *string        `gorm:"type:text" json:"config_value"`
	Description *string        `gorm:"size:500" json:"description"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}
