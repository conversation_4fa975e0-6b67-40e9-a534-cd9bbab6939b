# Comprehensive HTTP Logging Guide

This guide explains the comprehensive HTTP logging functionality that has been added to the Go server application.

## Features

The logging middleware provides detailed logging of all HTTP requests and responses with the following features:

### Request Logging
- HTTP method (GET, POST, PUT, DELETE, etc.)
- Full request URL and path
- Client IP address
- User-Agent header
- All request headers (with sensitive data masking)
- Request body content (JSON and text)
- Timestamp with RFC3339 format
- Unique request ID for correlation

### Response Logging
- HTTP status code
- Response headers (with sensitive data masking)
- Response body content (JSON and text)
- Response time/duration
- Error information (if any)
- Correlation with request using request ID

### Security Features
- **Sensitive Data Masking**: Automatically masks sensitive fields like passwords, tokens, secrets, authorization headers
- **Configurable Sensitive Fields**: Customize which fields should be masked
- **Body Size Limiting**: Prevents logging of extremely large request/response bodies

### Configuration Options
- **Enable/Disable**: Turn detailed logging on/off
- **Log Levels**: Control logging verbosity (debug, info, warn, error)
- **Selective Logging**: Choose what to log (headers, request body, response body)
- **Pretty Printing**: Format JSON logs for better readability

## Configuration

### Environment Variables

Add these environment variables to your `.env` file:

```bash
# Enable/disable detailed logging
LOG_ENABLED=true

# Log level: debug, info, warn, error
LOG_LEVEL=info

# Enable logging of request bodies
LOG_REQUEST_BODY=true

# Enable logging of response bodies
LOG_RESPONSE_BODY=true

# Enable logging of headers
LOG_HEADERS=true

# Maximum body size to log (in bytes)
LOG_MAX_BODY_SIZE=10240

# Pretty print JSON logs for readability
LOG_PRETTY_PRINT=true
```

### Configuration Structure

The logging configuration is defined in `server/config/config.go`:

```go
type LoggingConfig struct {
    Enabled           bool     `json:"enabled"`
    Level             string   `json:"level"`
    LogRequestBody    bool     `json:"log_request_body"`
    LogResponseBody   bool     `json:"log_response_body"`
    LogHeaders        bool     `json:"log_headers"`
    MaxBodySize       int      `json:"max_body_size"`
    SensitiveFields   []string `json:"sensitive_fields"`
    PrettyPrint       bool     `json:"pretty_print"`
}
```

## Log Format

Logs are output in structured JSON format. Here are examples:

### Request Log Example

```json
{
  "request_id": "1703123456789012345",
  "timestamp": "2023-12-21T10:30:45Z",
  "level": "info",
  "type": "request",
  "method": "POST",
  "url": "/v1/comments",
  "path": "/v1/comments",
  "client_ip": "*************",
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Be***er abc123def456",
    "Accept": "application/json"
  },
  "body": {
    "content": "This is a great drama!",
    "drama_id": 123,
    "rating": 5
  }
}
```

### Response Log Example

```json
{
  "request_id": "1703123456789012345",
  "timestamp": "2023-12-21T10:30:45Z",
  "level": "info",
  "type": "response",
  "status_code": 200,
  "response_time": "45.123ms",
  "headers": {
    "Content-Type": "application/json",
    "X-Request-ID": "1703123456789012345"
  },
  "body": {
    "code": 200,
    "message": "success",
    "data": {
      "id": 456,
      "content": "This is a great drama!",
      "created_at": "2023-12-21T10:30:45Z"
    }
  }
}
```

## Sensitive Data Masking

The following fields are automatically masked by default:
- `password`
- `token`
- `secret`
- `key`
- `authorization`
- `auth`
- `jwt`

Masking example: `"password": "my***rd"` (shows first 2 and last 2 characters)

## Usage in Different Environments

### Development Environment
```bash
LOG_ENABLED=true
LOG_LEVEL=debug
LOG_REQUEST_BODY=true
LOG_RESPONSE_BODY=true
LOG_HEADERS=true
LOG_PRETTY_PRINT=true
```

### Production Environment
```bash
LOG_ENABLED=true
LOG_LEVEL=info
LOG_REQUEST_BODY=false
LOG_RESPONSE_BODY=false
LOG_HEADERS=true
LOG_PRETTY_PRINT=false
```

### Testing Environment
```bash
LOG_ENABLED=false
```

## Performance Considerations

- **Body Logging**: Disable request/response body logging in high-traffic production environments
- **Log Level**: Use `info` or `warn` in production to reduce log volume
- **Body Size Limit**: Keep `LOG_MAX_BODY_SIZE` reasonable (default: 10KB)
- **Pretty Printing**: Disable in production for better performance

## Integration

The logging middleware is automatically integrated into your Gin router in `server/routes/routes.go`:

```go
router.Use(middleware.Logger(cfg))
```

## Request Correlation

Each request gets a unique `request_id` that appears in both request and response logs, making it easy to trace the complete request lifecycle.

## Troubleshooting

1. **No logs appearing**: Check that `LOG_ENABLED=true`
2. **Too verbose**: Increase log level to `warn` or `error`
3. **Missing body content**: Ensure `LOG_REQUEST_BODY=true` and `LOG_RESPONSE_BODY=true`
4. **Performance issues**: Disable body logging and pretty printing in production
