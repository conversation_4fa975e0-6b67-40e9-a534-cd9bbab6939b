{"name": "52kanduanju.mp", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "default", "typescript": false, "css": "Sass", "framework": "Vue3"}, "scripts": {"prepare": "husky", "new": "taro new", "build:weapp": "taro build --type weapp", "build:weapp:dev": "NODE_ENV=development TARO_ENV=weapp node scripts/generate-project-config.js && taro build --type weapp", "build:weapp:test": "NODE_ENV=test TARO_ENV=weapp node scripts/generate-project-config.js && taro build --type weapp", "build:weapp:prod": "NODE_ENV=production TARO_ENV=weapp node scripts/generate-project-config.js && taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:h5:dev": "NODE_ENV=development taro build --type h5", "build:h5:test": "NODE_ENV=test taro build --type h5", "build:h5:prod": "NODE_ENV=production taro build --type h5", "build:h5:analyze": "NODE_ENV=production ANALYZE=true taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "npm run build:weapp:dev -- --watch", "dev:weapp:test": "npm run build:weapp:test -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5:dev -- --watch", "dev:h5:test": "npm run build:h5:test -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch", "analyze": "npm run build:h5:analyze", "postinstall": "weapp-tw patch"}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@babel/runtime": "^7.24.4", "@tailwindcss/cli": "^4.1.11", "@tarojs/binding-darwin-x64": "^4.1.3", "@tarojs/components": "4.1.3", "@tarojs/helper": "4.1.3", "@tarojs/plugin-framework-vue3": "4.1.3", "@tarojs/plugin-platform-alipay": "4.1.3", "@tarojs/plugin-platform-h5": "4.1.3", "@tarojs/plugin-platform-harmony-hybrid": "4.1.3", "@tarojs/plugin-platform-jd": "4.1.3", "@tarojs/plugin-platform-qq": "4.1.3", "@tarojs/plugin-platform-swan": "4.1.3", "@tarojs/plugin-platform-tt": "4.1.3", "@tarojs/plugin-platform-weapp": "4.1.3", "@tarojs/runtime": "4.1.3", "@tarojs/shared": "4.1.3", "@tarojs/taro": "4.1.3", "pinia": "^2.1.7", "vue": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-transform-class-properties": "7.25.9", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@tarojs/binding-darwin-arm64": "^4.1.4", "@tarojs/cli": "4.1.3", "@tarojs/plugin-framework-react": "4.1.3", "@tarojs/plugin-generator": "4.1.3", "@tarojs/vite-runner": "4.1.3", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.21", "babel-preset-taro": "4.1.3", "eslint": "^8.57.0", "eslint-config-taro": "4.1.3", "eslint-plugin-vue": "^9.17.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.4.38", "sass": "^1.75.0", "stylelint": "^16.4.0", "stylelint-config-standard": "^38.0.0", "tailwindcss": "^3.3.3", "terser": "^5.30.4", "vite": "^4.2.0", "weapp-tailwindcss": "^4.1.10", "webpack-bundle-analyzer": "^4.10.2"}}