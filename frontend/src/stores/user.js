import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import Taro from '@tarojs/taro'
import { api } from '@/utils/api'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)
  const token = ref('')
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)

  // 初始化用户状态（从本地存储恢复）
  const initUserState = () => {
    try {
      const savedToken = Taro.getStorageSync('token')
      const savedUserInfo = Taro.getStorageSync('userInfo')
      
      if (savedToken) {
        token.value = savedToken
      }
      if (savedUserInfo) {
        userInfo.value = savedUserInfo
      }
    } catch (error) {
      console.error('初始化用户状态失败:', error)
    }
  }

  // 保存用户状态到本地存储
  const saveUserState = () => {
    try {
      if (token.value) {
        Taro.setStorageSync('token', token.value)
      }
      if (userInfo.value) {
        Taro.setStorageSync('userInfo', userInfo.value)
      }
    } catch (error) {
      console.error('保存用户状态失败:', error)
    }
  }

  // 清除用户状态
  const clearUserState = () => {
    userInfo.value = null
    token.value = ''
    try {
      Taro.removeStorageSync('token')
      Taro.removeStorageSync('userInfo')
    } catch (error) {
      console.error('清除用户状态失败:', error)
    }
  }

  // 微信登录
  const wechatLogin = async () => {
    try {
      // 1. 获取微信登录code
      const loginRes = await Taro.login()
      if (!loginRes.code) {
        throw new Error('获取微信登录code失败')
      }

      // 2. 调用后端登录接口
      const response = await api.wechatLogin({
        code: loginRes.code
      })

      if (response.code === 200) {
        // 登录成功，保存token和用户信息
        token.value = response.data.token
        userInfo.value = response.data.user
        saveUserState()
        return { success: true, data: response.data }
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 获取用户信息授权
  const getUserProfile = async () => {
    try {
      const userProfile = await Taro.getUserProfile({
        desc: '用于完善用户资料'
      })
      return { success: true, data: userProfile.userInfo }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 获取手机号授权
  const getPhoneNumber = async (e) => {
    try {
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        // 调用后端接口解密手机号
        const response = await api.decryptPhoneNumber({
          encryptedData: e.detail.encryptedData,
          iv: e.detail.iv,
          sessionKey: userInfo.value?.sessionKey
        })
        
        if (response.code === 200) {
          // 更新用户信息中的手机号
          userInfo.value = {
            ...userInfo.value,
            phone: response.data.phoneNumber
          }
          saveUserState()
          return { success: true, data: response.data }
        } else {
          throw new Error(response.message || '获取手机号失败')
        }
      } else {
        throw new Error('用户拒绝授权手机号')
      }
    } catch (error) {
      console.error('获取手机号失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 更新用户信息
  const updateUserInfo = async (updateData) => {
    try {
      const response = await api.updateUserInfo(updateData)
      if (response.code === 200) {
        userInfo.value = { ...userInfo.value, ...response.data }
        saveUserState()
        return { success: true, data: response.data }
      } else {
        throw new Error(response.message || '更新用户信息失败')
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 登出
  const logout = () => {
    clearUserState()
    // 可以添加调用后端登出接口的逻辑
  }

  // 检查登录状态
  const checkLoginStatus = () => {
    if (!isLoggedIn.value) {
      return false
    }
    
    // 可以添加token有效性检查
    return true
  }

  // 获取用户头像URL（处理微信头像）
  const getUserAvatarUrl = computed(() => {
    if (!userInfo.value?.avatar) {
      return ''
    }
    
    // 如果是微信头像URL，可能需要特殊处理
    return userInfo.value.avatar
  })

  // 获取用户显示名称
  const getUserDisplayName = computed(() => {
    if (!userInfo.value) {
      return '未登录'
    }
    
    return userInfo.value.nickname || userInfo.value.username || '用户'
  })

  return {
    // 状态
    userInfo,
    token,
    isLoggedIn,
    
    // 计算属性
    getUserAvatarUrl,
    getUserDisplayName,
    
    // 方法
    initUserState,
    saveUserState,
    clearUserState,
    wechatLogin,
    getUserProfile,
    getPhoneNumber,
    updateUserInfo,
    logout,
    checkLoginStatus
  }
})
