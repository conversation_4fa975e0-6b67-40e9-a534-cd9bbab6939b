<template>
  <view v-if="visible" class="login-modal-overlay" @tap="onOverlayClick">
    <view class="login-modal" @tap.stop>
      <!-- 第一步：获取用户信息授权 -->
      <view v-if="currentStep === 1" class="login-step">
        <view class="modal-header">
          <view class="modal-close" @tap="onClose">
            <text class="material-icons">close</text>
          </view>
        </view>
        
        <view class="login-content">
          <view class="login-icon">
            <text class="material-icons">account_circle</text>
          </view>
          
          <view class="login-title">完善个人信息</view>
          <view class="login-desc">获取您的头像和昵称，用于个性化体验</view>
          
          <button 
            class="login-btn primary" 
            @tap="onGetUserProfile"
            :disabled="loading"
          >
            <text v-if="loading">获取中...</text>
            <text v-else>授权头像昵称</text>
          </button>
          
          <view class="login-skip" @tap="onSkipUserProfile">
            <text>暂不授权</text>
          </view>
        </view>
      </view>

      <!-- 第二步：获取手机号授权 -->
      <view v-if="currentStep === 2" class="login-step">
        <view class="modal-header">
          <view class="modal-close" @tap="onClose">
            <text class="material-icons">close</text>
          </view>
        </view>
        
        <view class="login-content">
          <view class="login-icon">
            <text class="material-icons">phone</text>
          </view>
          
          <view class="login-title">绑定手机号</view>
          <view class="login-desc">绑定手机号，享受更安全的服务</view>
          
          <button 
            class="login-btn primary" 
            open-type="getPhoneNumber"
            @getphonenumber="onGetPhoneNumber"
            :disabled="loading"
          >
            <text v-if="loading">获取中...</text>
            <text v-else>授权手机号</text>
          </button>
          
          <view class="login-skip" @tap="onSkipPhoneNumber">
            <text>暂不绑定</text>
          </view>
        </view>
      </view>

      <!-- 登录成功 -->
      <view v-if="currentStep === 3" class="login-step">
        <view class="login-content">
          <view class="login-icon success">
            <text class="material-icons">check_circle</text>
          </view>
          
          <view class="login-title">登录成功</view>
          <view class="login-desc">欢迎来到52看短剧</view>
          
          <button class="login-btn primary" @tap="onLoginComplete">
            <text>开始使用</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue'
import Taro from '@tarojs/taro'
import { useUserStore } from '@/stores/user'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'success'])

const userStore = useUserStore()
const currentStep = ref(1)
const loading = ref(false)
const userProfileData = ref(null)

// 监听弹窗显示状态，重置步骤
watch(() => props.visible, (newVal) => {
  if (newVal) {
    currentStep.value = 1
    userProfileData.value = null
  }
})

// 关闭弹窗
const onClose = () => {
  emit('close')
}

// 点击遮罩层关闭
const onOverlayClick = () => {
  onClose()
}

// 获取用户信息授权
const onGetUserProfile = async () => {
  loading.value = true
  
  try {
    const result = await userStore.getUserProfile()
    if (result.success) {
      userProfileData.value = result.data
      
      // 先进行微信登录
      const loginResult = await userStore.wechatLogin()
      if (loginResult.success) {
        // 更新用户信息（头像和昵称）
        await userStore.updateUserInfo({
          nickname: result.data.nickName,
          avatar: result.data.avatarUrl
        })
        
        // 进入下一步
        currentStep.value = 2
      } else {
        throw new Error(loginResult.error)
      }
    } else {
      throw new Error(result.error)
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    Taro.showToast({
      title: error.message || '获取用户信息失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 跳过用户信息授权
const onSkipUserProfile = async () => {
  loading.value = true
  
  try {
    // 直接进行微信登录
    const loginResult = await userStore.wechatLogin()
    if (loginResult.success) {
      // 进入下一步
      currentStep.value = 2
    } else {
      throw new Error(loginResult.error)
    }
  } catch (error) {
    console.error('登录失败:', error)
    Taro.showToast({
      title: error.message || '登录失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 获取手机号授权
const onGetPhoneNumber = async (e) => {
  loading.value = true
  
  try {
    const result = await userStore.getPhoneNumber(e)
    if (result.success) {
      // 进入成功页面
      currentStep.value = 3
      
      // 1秒后自动关闭
      setTimeout(() => {
        onLoginComplete()
      }, 1000)
    } else {
      throw new Error(result.error)
    }
  } catch (error) {
    console.error('获取手机号失败:', error)
    Taro.showToast({
      title: error.message || '获取手机号失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 跳过手机号授权
const onSkipPhoneNumber = () => {
  // 进入成功页面
  currentStep.value = 3
  
  // 1秒后自动关闭
  setTimeout(() => {
    onLoginComplete()
  }, 1000)
}

// 登录完成
const onLoginComplete = () => {
  emit('success')
  emit('close')
}
</script>

<style lang="scss" scoped>
.login-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.login-modal {
  background: #fff;
  border-radius: 24rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  position: relative;
  padding: 32rpx;
  
  .modal-close {
    position: absolute;
    top: 32rpx;
    right: 32rpx;
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    
    .material-icons {
      font-size: 32rpx;
    }
  }
}

.login-content {
  padding: 0 48rpx 48rpx;
  text-align: center;
}

.login-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
  
  .material-icons {
    font-size: 64rpx;
    color: #6b7280;
  }
  
  &.success {
    background: #dcfce7;
    
    .material-icons {
      color: #16a34a;
    }
  }
}

.login-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16rpx;
}

.login-desc {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  margin-bottom: 24rpx;
  
  &.primary {
    background: #4f46e5;
    color: #fff;
    
    &:disabled {
      background: #9ca3af;
    }
  }
}

.login-skip {
  color: #6b7280;
  font-size: 28rpx;
  padding: 16rpx;
  
  text {
    text-decoration: underline;
  }
}
</style>
