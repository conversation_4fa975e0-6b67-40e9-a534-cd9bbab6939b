import { useUserStore } from '@/stores/user'

/**
 * 检查用户是否已登录，如果未登录则显示登录弹窗
 * @param {Function} showLoginModal - 显示登录弹窗的函数
 * @returns {boolean} 是否已登录
 */
export const checkLogin = (showLoginModal) => {
  const userStore = useUserStore()
  
  if (!userStore.isLoggedIn) {
    if (showLoginModal && typeof showLoginModal === 'function') {
      showLoginModal()
    }
    return false
  }
  
  return true
}

/**
 * 需要登录的操作装饰器
 * @param {Function} operation - 需要执行的操作
 * @param {Function} showLoginModal - 显示登录弹窗的函数
 * @returns {Function} 装饰后的操作函数
 */
export const requireLogin = (operation, showLoginModal) => {
  return async (...args) => {
    if (!checkLogin(showLoginModal)) {
      return { success: false, error: '请先登录' }
    }
    
    try {
      return await operation(...args)
    } catch (error) {
      // 如果是认证错误，显示登录弹窗
      if (error.message && error.message.includes('登录')) {
        if (showLoginModal && typeof showLoginModal === 'function') {
          showLoginModal()
        }
      }
      throw error
    }
  }
}

/**
 * 获取用户信息
 * @returns {Object|null} 用户信息
 */
export const getUserInfo = () => {
  const userStore = useUserStore()
  return userStore.userInfo
}

/**
 * 获取用户token
 * @returns {string} 用户token
 */
export const getToken = () => {
  const userStore = useUserStore()
  return userStore.token
}

/**
 * 检查是否是微信小程序环境
 * @returns {boolean} 是否是微信小程序
 */
export const isWeapp = () => {
  return process.env.TARO_ENV === 'weapp'
}
